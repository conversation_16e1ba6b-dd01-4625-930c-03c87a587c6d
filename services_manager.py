#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير الخدمات المتقدم للبوت التعليمي
Advanced Services Manager for Educational Bot

يدير إنشاء وتخصيص الخدمات والمختصين بشكل متقدم
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
from firebase_manager import firebase_manager

logger = logging.getLogger(__name__)

class ServicesManager:
    """مدير الخدمات المتقدم"""
    
    def __init__(self):
        self.services_data = {}
        self.specialists_data = {}
        self.service_orders = {}
        self.load_services_from_firebase()
    
    def load_services_from_firebase(self):
        """تحميل بيانات الخدمات من Firebase"""
        try:
            self.services_data = firebase_manager.get_services_data() or self.get_default_services()
            self.specialists_data = firebase_manager.get_specialists_data() or self.get_default_specialists()
            self.service_orders = firebase_manager.get_service_orders() or {}
            logger.info("✅ تم تحميل بيانات الخدمات من Firebase")
        except Exception as e:
            logger.error(f"❌ خطأ في تحميل بيانات الخدمات: {e}")
            self.services_data = self.get_default_services()
            self.specialists_data = self.get_default_specialists()
            self.service_orders = {}
    
    def get_default_services(self) -> Dict[str, Any]:
        """الخدمات الافتراضية"""
        return {
            "categories": {
                "التصميم والجرافيك": {
                    "id": "design_graphics",
                    "icon": "🎨",
                    "description": "خدمات التصميم الجرافيكي والإبداعي المتخصصة",
                    "order": 1,
                    "active": True,
                    "services": {
                        "تصميم الشعارات": {
                            "id": "logo_design",
                            "icon": "🏷️",
                            "description": "تصميم شعارات احترافية ومميزة تعكس هوية علامتك التجارية",
                            "detailed_description": """
🎯 **خدمة تصميم الشعارات الاحترافية**

📋 **ما نقدمه:**
• تصميم شعار أصلي 100% حسب متطلباتك
• 3 مفاهيم تصميمية مختلفة للاختيار من بينها
• تعديلات مجانية حتى الوصول للنتيجة المطلوبة
• ملفات عالية الجودة بصيغ مختلفة (PNG, JPG, SVG, AI)
• دليل استخدام الشعار والألوان

🎨 **أنواع الشعارات:**
• شعارات نصية (Typography)
• شعارات رمزية (Symbol/Icon)
• شعارات مختلطة (Text + Symbol)
• شعارات مصغرة (Favicon)

⏱️ **مدة التسليم:** 3-7 أيام عمل
💰 **السعر:** يبدأ من 50 دولار
🔄 **التعديلات:** 3 جولات تعديل مجانية
                            """,
                            "price_range": "50-200 دولار",
                            "delivery_time": "3-7 أيام",
                            "features": [
                                "تصميم أصلي 100%",
                                "ملفات عالية الجودة",
                                "3 مفاهيم تصميمية",
                                "تعديلات مجانية",
                                "دليل استخدام الشعار",
                                "دعم فني مجاني"
                            ],
                            "packages": {
                                "أساسي": {
                                    "price": 50,
                                    "features": ["شعار واحد", "ملف PNG", "تعديل واحد"],
                                    "delivery": "3 أيام"
                                },
                                "متقدم": {
                                    "price": 100,
                                    "features": ["3 مفاهيم", "ملفات متعددة", "3 تعديلات"],
                                    "delivery": "5 أيام"
                                },
                                "احترافي": {
                                    "price": 200,
                                    "features": ["5 مفاهيم", "جميع الملفات", "تعديلات مفتوحة", "دليل الاستخدام"],
                                    "delivery": "7 أيام"
                                }
                            },
                            "portfolio": [
                                {
                                    "title": "شعار شركة تقنية",
                                    "image_url": "",
                                    "description": "شعار حديث لشركة تطوير البرمجيات"
                                }
                            ],
                            "specialists": ["ahmed_designer", "sara_creative"],
                            "tags": ["شعار", "لوجو", "هوية بصرية", "تصميم"],
                            "active": True,
                            "created_date": datetime.now().isoformat()
                        },
                        "تصميم البوسترات": {
                            "id": "poster_design",
                            "icon": "📄",
                            "description": "تصميم بوسترات إعلانية وتسويقية جذابة",
                            "detailed_description": """
🎯 **خدمة تصميم البوسترات الإعلانية**

📋 **ما نقدمه:**
• تصميم بوسترات احترافية للأحداث والمناسبات
• تصميم إعلانات تسويقية جذابة
• بوسترات وسائل التواصل الاجتماعي
• تصميم منشورات ترويجية

🎨 **أنواع البوسترات:**
• بوسترات الأحداث والمؤتمرات
• إعلانات المنتجات والخدمات
• بوسترات تعليمية وتوعوية
• منشورات وسائل التواصل

⏱️ **مدة التسليم:** 2-5 أيام عمل
💰 **السعر:** يبدأ من 30 دولار
                            """,
                            "price_range": "30-150 دولار",
                            "delivery_time": "2-5 أيام",
                            "features": [
                                "تصميم احترافي",
                                "ألوان جذابة",
                                "نصوص واضحة",
                                "تعديلات مجانية"
                            ],
                            "specialists": ["ahmed_designer", "layla_graphics"],
                            "active": True
                        }
                    }
                },
                "البرمجة والتطوير": {
                    "id": "programming_dev",
                    "icon": "💻",
                    "description": "خدمات البرمجة وتطوير التطبيقات والمواقع",
                    "order": 2,
                    "active": True,
                    "services": {
                        "تطوير المواقع": {
                            "id": "web_development",
                            "icon": "🌐",
                            "description": "تطوير مواقع ويب احترافية ومتجاوبة",
                            "detailed_description": """
🎯 **خدمة تطوير المواقع الاحترافية**

📋 **ما نقدمه:**
• تطوير مواقع ويب متجاوبة مع جميع الأجهزة
• تصميم واجهات مستخدم حديثة وجذابة
• برمجة خلفية قوية وآمنة
• تحسين محركات البحث (SEO)
• لوحة تحكم سهلة الاستخدام

💻 **التقنيات المستخدمة:**
• Frontend: HTML5, CSS3, JavaScript, React
• Backend: Node.js, Python, PHP
• قواعد البيانات: MySQL, MongoDB
• استضافة وحماية SSL

⏱️ **مدة التسليم:** 2-8 أسابيع
💰 **السعر:** يبدأ من 500 دولار
                            """,
                            "price_range": "500-5000 دولار",
                            "delivery_time": "2-8 أسابيع",
                            "features": [
                                "تصميم متجاوب",
                                "سرعة تحميل عالية",
                                "SEO محسن",
                                "لوحة تحكم",
                                "دعم فني مجاني"
                            ],
                            "specialists": ["mohammed_dev", "omar_fullstack"],
                            "active": True
                        },
                        "تطوير التطبيقات": {
                            "id": "app_development",
                            "icon": "📱",
                            "description": "تطوير تطبيقات الجوال للأندرويد والآيفون",
                            "price_range": "1000-10000 دولار",
                            "delivery_time": "4-12 أسبوع",
                            "specialists": ["ali_mobile", "fatima_flutter"],
                            "active": True
                        }
                    }
                },
                "التسويق الرقمي": {
                    "id": "digital_marketing",
                    "icon": "📈",
                    "description": "خدمات التسويق الرقمي ووسائل التواصل الاجتماعي",
                    "order": 3,
                    "active": True,
                    "services": {
                        "إدارة وسائل التواصل": {
                            "id": "social_media_management",
                            "icon": "📱",
                            "description": "إدارة حسابات وسائل التواصل الاجتماعي",
                            "price_range": "200-800 دولار شهرياً",
                            "delivery_time": "خدمة شهرية",
                            "specialists": ["nour_marketing", "hassan_social"],
                            "active": True
                        }
                    }
                }
            },
            "settings": {
                "enable_quotes": True,
                "show_portfolio": True,
                "enable_reviews": True,
                "contact_method": "telegram",
                "auto_assign_specialists": False,
                "require_approval": True
            },
            "statistics": {
                "total_services": 0,
                "total_orders": 0,
                "active_specialists": 0,
                "completed_orders": 0
            }
        }
    
    def get_default_specialists(self) -> Dict[str, Any]:
        """المختصين الافتراضيين"""
        return {
            "ahmed_designer": {
                "id": "ahmed_designer",
                "name": "أحمد محمد",
                "title": "مصمم جرافيك محترف",
                "bio": "مصمم جرافيك مع خبرة 5 سنوات في تصميم الشعارات والهويات البصرية",
                "experience": "5 سنوات",
                "specialties": ["تصميم الشعارات", "الهوية البصرية", "تصميم البوسترات"],
                "contact": {
                    "telegram": "@ahmed_designer",
                    "email": "<EMAIL>",
                    "phone": "+964xxxxxxxxx"
                },
                "portfolio": [
                    {
                        "title": "مشروع شعار شركة تقنية",
                        "description": "تصميم شعار وهوية بصرية كاملة",
                        "image_url": "",
                        "date": "2024-01-15"
                    }
                ],
                "ratings": {
                    "average": 4.8,
                    "total_reviews": 25,
                    "five_star": 20,
                    "four_star": 4,
                    "three_star": 1,
                    "two_star": 0,
                    "one_star": 0
                },
                "availability": {
                    "status": "متاح",
                    "working_hours": "9:00 AM - 6:00 PM",
                    "timezone": "Baghdad",
                    "response_time": "خلال ساعة"
                },
                "services": ["logo_design", "poster_design"],
                "active": True,
                "joined_date": "2023-01-01"
            },
            "mohammed_dev": {
                "id": "mohammed_dev",
                "name": "محمد أحمد",
                "title": "مطور ويب متكامل",
                "bio": "مطور ويب متخصص في تطوير المواقع والتطبيقات باستخدام أحدث التقنيات",
                "experience": "7 سنوات",
                "specialties": ["تطوير المواقع", "React", "Node.js", "قواعد البيانات"],
                "contact": {
                    "telegram": "@mohammed_dev",
                    "email": "<EMAIL>"
                },
                "ratings": {
                    "average": 4.9,
                    "total_reviews": 35
                },
                "services": ["web_development"],
                "active": True
            }
        }
    
    def get_service_categories(self) -> List[Dict[str, Any]]:
        """الحصول على فئات الخدمات"""
        try:
            categories = []
            for cat_name, cat_data in self.services_data.get("categories", {}).items():
                if cat_data.get("active", True):
                    categories.append({
                        "name": cat_name,
                        "data": cat_data
                    })
            
            # ترتيب حسب الأولوية
            categories.sort(key=lambda x: x["data"].get("order", 999))
            return categories
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على فئات الخدمات: {e}")
            return []
    
    def get_category_services(self, category_name: str) -> List[Dict[str, Any]]:
        """الحصول على خدمات فئة محددة"""
        try:
            category = self.services_data.get("categories", {}).get(category_name, {})
            services = []
            
            for service_name, service_data in category.get("services", {}).items():
                if service_data.get("active", True):
                    services.append({
                        "name": service_name,
                        "data": service_data
                    })
            
            return services
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على خدمات الفئة: {e}")
            return []
    
    def get_service_details(self, category_name: str, service_name: str) -> Optional[Dict[str, Any]]:
        """الحصول على تفاصيل خدمة محددة"""
        try:
            category = self.services_data.get("categories", {}).get(category_name, {})
            service = category.get("services", {}).get(service_name, {})
            
            if service and service.get("active", True):
                # إضافة معلومات المختصين
                specialists_info = []
                for specialist_id in service.get("specialists", []):
                    specialist = self.specialists_data.get(specialist_id, {})
                    if specialist and specialist.get("active", True):
                        specialists_info.append(specialist)
                
                service["specialists_info"] = specialists_info
                return service
            
            return None
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على تفاصيل الخدمة: {e}")
            return None
    
    def add_service_category(self, category_name: str, category_data: Dict[str, Any]) -> bool:
        """إضافة فئة خدمات جديدة"""
        try:
            if "categories" not in self.services_data:
                self.services_data["categories"] = {}
            
            category_data["id"] = category_data.get("id", category_name.lower().replace(" ", "_"))
            category_data["created_date"] = datetime.now().isoformat()
            category_data["active"] = True
            
            if "services" not in category_data:
                category_data["services"] = {}
            
            self.services_data["categories"][category_name] = category_data
            
            success = firebase_manager.save_services_data(self.services_data)
            if success:
                logger.info(f"✅ تم إضافة فئة الخدمات: {category_name}")
            return success
        except Exception as e:
            logger.error(f"❌ خطأ في إضافة فئة الخدمات: {e}")
            return False
    
    def add_service(self, category_name: str, service_name: str, service_data: Dict[str, Any]) -> bool:
        """إضافة خدمة جديدة"""
        try:
            if category_name not in self.services_data.get("categories", {}):
                return False
            
            category = self.services_data["categories"][category_name]
            if "services" not in category:
                category["services"] = {}
            
            service_data["id"] = service_data.get("id", service_name.lower().replace(" ", "_"))
            service_data["created_date"] = datetime.now().isoformat()
            service_data["active"] = True
            
            category["services"][service_name] = service_data
            
            success = firebase_manager.save_services_data(self.services_data)
            if success:
                logger.info(f"✅ تم إضافة الخدمة: {service_name}")
                self._update_statistics()
            return success
        except Exception as e:
            logger.error(f"❌ خطأ في إضافة الخدمة: {e}")
            return False
    
    def add_specialist(self, specialist_data: Dict[str, Any]) -> bool:
        """إضافة مختص جديد"""
        try:
            specialist_id = specialist_data.get("id") or specialist_data.get("name", "").lower().replace(" ", "_")
            specialist_data["id"] = specialist_id
            specialist_data["joined_date"] = datetime.now().isoformat()
            specialist_data["active"] = True
            
            if "ratings" not in specialist_data:
                specialist_data["ratings"] = {
                    "average": 0,
                    "total_reviews": 0
                }
            
            self.specialists_data[specialist_id] = specialist_data
            
            success = firebase_manager.save_specialists_data(self.specialists_data)
            if success:
                logger.info(f"✅ تم إضافة المختص: {specialist_data.get('name')}")
                self._update_statistics()
            return success
        except Exception as e:
            logger.error(f"❌ خطأ في إضافة المختص: {e}")
            return False
    
    def create_service_order(self, user_id: int, service_info: Dict[str, Any], order_details: Dict[str, Any]) -> str:
        """إنشاء طلب خدمة جديد"""
        try:
            order_id = f"order_{user_id}_{int(datetime.now().timestamp())}"
            
            order_data = {
                "id": order_id,
                "user_id": user_id,
                "service_category": service_info.get("category"),
                "service_name": service_info.get("name"),
                "service_id": service_info.get("id"),
                "order_details": order_details,
                "status": "pending",  # pending, assigned, in_progress, completed, cancelled
                "created_date": datetime.now().isoformat(),
                "assigned_specialist": None,
                "estimated_delivery": None,
                "actual_delivery": None,
                "price": None,
                "notes": []
            }
            
            self.service_orders[order_id] = order_data
            
            success = firebase_manager.save_service_orders(self.service_orders)
            if success:
                logger.info(f"✅ تم إنشاء طلب الخدمة: {order_id}")
                self._update_statistics()
            
            return order_id if success else None
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء طلب الخدمة: {e}")
            return None
    
    def get_specialist_info(self, specialist_id: str) -> Optional[Dict[str, Any]]:
        """الحصول على معلومات مختص"""
        return self.specialists_data.get(specialist_id)
    
    def get_user_orders(self, user_id: int) -> List[Dict[str, Any]]:
        """الحصول على طلبات المستخدم"""
        try:
            user_orders = []
            for order_id, order_data in self.service_orders.items():
                if order_data.get("user_id") == user_id:
                    user_orders.append(order_data)
            
            # ترتيب حسب التاريخ (الأحدث أولاً)
            user_orders.sort(key=lambda x: x.get("created_date", ""), reverse=True)
            return user_orders
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على طلبات المستخدم: {e}")
            return []
    
    def _update_statistics(self):
        """تحديث الإحصائيات"""
        try:
            stats = self.services_data.get("statistics", {})
            
            # عدد الخدمات
            total_services = 0
            for category in self.services_data.get("categories", {}).values():
                total_services += len(category.get("services", {}))
            
            stats["total_services"] = total_services
            stats["total_orders"] = len(self.service_orders)
            stats["active_specialists"] = len([s for s in self.specialists_data.values() if s.get("active", True)])
            stats["completed_orders"] = len([o for o in self.service_orders.values() if o.get("status") == "completed"])
            
            self.services_data["statistics"] = stats
        except Exception as e:
            logger.error(f"❌ خطأ في تحديث الإحصائيات: {e}")
    
    def reload_services(self):
        """إعادة تحميل بيانات الخدمات من Firebase"""
        self.load_services_from_firebase()

# إنشاء مثيل مدير الخدمات
services_manager = ServicesManager()
