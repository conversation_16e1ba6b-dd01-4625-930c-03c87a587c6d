#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
محرر الأزرار التفاعلي للبوت التعليمي
Interactive Button Editor for Educational Bot

يوفر واجهة تفاعلية لإنشاء وتحرير الأزرار الديناميكية
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
from telebot import types
from button_manager import button_manager

logger = logging.getLogger(__name__)

class ButtonEditor:
    """محرر الأزرار التفاعلي"""
    
    def __init__(self, bot):
        self.bot = bot
        self.editing_sessions = {}  # جلسات تحرير الأزرار النشطة
        self.button_templates = self.get_button_templates()
    
    def get_button_templates(self) -> Dict[str, Any]:
        """قوالب الأزرار المتاحة"""
        return {
            "simple_keyboard": {
                "name": "كيبورد بسيط",
                "description": "أزرار كيبورد عادية للتنقل",
                "type": "keyboard",
                "template": {
                    "type": "keyboard",
                    "layout": "2x2",
                    "buttons": [
                        {
                            "text": "زر 1",
                            "action": "section",
                            "target": "",
                            "row": 1,
                            "position": 1
                        },
                        {
                            "text": "زر 2", 
                            "action": "section",
                            "target": "",
                            "row": 1,
                            "position": 2
                        }
                    ]
                }
            },
            "inline_menu": {
                "name": "قائمة إنلاين",
                "description": "أزرار إنلاين للعمليات السريعة",
                "type": "inline",
                "template": {
                    "type": "inline",
                    "layout": "2x3",
                    "buttons": [
                        {
                            "text": "خيار 1",
                            "callback_data": "option_1",
                            "row": 1,
                            "position": 1
                        },
                        {
                            "text": "خيار 2",
                            "callback_data": "option_2", 
                            "row": 1,
                            "position": 2
                        }
                    ]
                }
            },
            "navigation_buttons": {
                "name": "أزرار التنقل",
                "description": "أزرار للتنقل بين الأقسام",
                "type": "keyboard",
                "template": {
                    "type": "keyboard",
                    "layout": "1x3",
                    "buttons": [
                        {
                            "text": "🔙 السابق",
                            "action": "back",
                            "target": "",
                            "row": 1,
                            "position": 1
                        },
                        {
                            "text": "🏠 الرئيسية",
                            "action": "main_menu",
                            "target": "",
                            "row": 1,
                            "position": 2
                        },
                        {
                            "text": "التالي ⏭️",
                            "action": "next",
                            "target": "",
                            "row": 1,
                            "position": 3
                        }
                    ]
                }
            },
            "social_links": {
                "name": "روابط التواصل",
                "description": "أزرار للروابط الخارجية",
                "type": "inline",
                "template": {
                    "type": "inline",
                    "layout": "2x2",
                    "buttons": [
                        {
                            "text": "📱 تليجرام",
                            "url": "https://t.me/username",
                            "row": 1,
                            "position": 1
                        },
                        {
                            "text": "🌐 الموقع",
                            "url": "https://example.com",
                            "row": 1,
                            "position": 2
                        }
                    ]
                }
            }
        }
    
    def show_button_management(self, call):
        """عرض إدارة الأزرار"""
        try:
            keyboard = types.InlineKeyboardMarkup(row_width=2)
            
            # الحصول على تكوينات الأزرار المتاحة
            button_configs = button_manager.get_all_button_configs()
            
            for config_name, config_data in button_configs.items():
                button_type = config_data.get("type", "keyboard")
                buttons_count = len(config_data.get("buttons", []))
                
                btn = types.InlineKeyboardButton(
                    f"{'⌨️' if button_type == 'keyboard' else '🔘'} {config_name} ({buttons_count})",
                    callback_data=f"button_edit_config_{config_name}"
                )
                keyboard.add(btn)
            
            # أزرار إضافية
            btn_create_new = types.InlineKeyboardButton(
                "➕ إنشاء تكوين جديد",
                callback_data="button_create_new"
            )
            btn_templates = types.InlineKeyboardButton(
                "📋 القوالب الجاهزة",
                callback_data="button_templates"
            )
            btn_preview = types.InlineKeyboardButton(
                "👁️ معاينة الأزرار",
                callback_data="button_preview_all"
            )
            btn_export = types.InlineKeyboardButton(
                "📤 تصدير التكوينات",
                callback_data="button_export"
            )
            btn_import = types.InlineKeyboardButton(
                "📥 استيراد التكوينات",
                callback_data="button_import"
            )
            btn_back = types.InlineKeyboardButton(
                "🔙 العودة للوحة التحكم",
                callback_data="admin_back"
            )
            
            keyboard.add(btn_create_new)
            keyboard.add(btn_templates, btn_preview)
            keyboard.add(btn_export, btn_import)
            keyboard.add(btn_back)
            
            text = f"""
🔘 **إدارة الأزرار الديناميكية**

📊 **الإحصائيات:**
• تكوينات الأزرار: {len(button_configs)}
• أنواع الأزرار المدعومة: Keyboard, Inline
• القوالب الجاهزة: {len(self.button_templates)}

🎛️ **التكوينات المتاحة:**
"""
            
            if button_configs:
                for config_name, config_data in list(button_configs.items())[:5]:
                    button_type = config_data.get("type", "keyboard")
                    buttons_count = len(config_data.get("buttons", []))
                    layout = config_data.get("layout", "غير محدد")
                    
                    text += f"• **{config_name}** ({button_type})\n"
                    text += f"  الأزرار: {buttons_count} | التخطيط: {layout}\n\n"
                
                if len(button_configs) > 5:
                    text += f"... و {len(button_configs) - 5} تكوينات أخرى\n\n"
            else:
                text += "لا توجد تكوينات أزرار حالياً\n\n"
            
            text += """
🎯 **العمليات المتاحة:**

➕ **إنشاء تكوين جديد**
• إنشاء مجموعة أزرار جديدة من الصفر
• تخصيص النصوص والإجراءات
• تحديد التخطيط والترتيب

📋 **القوالب الجاهزة**
• استخدام قوالب معدة مسبقاً
• تخصيص القوالب حسب الحاجة
• توفير الوقت والجهد

👁️ **معاينة الأزرار**
• اختبار الأزرار قبل النشر
• معاينة التخطيط والشكل
• التأكد من صحة الإجراءات

📤📥 **الاستيراد والتصدير**
• تصدير التكوينات للمشاركة
• استيراد تكوينات من ملفات خارجية
• نسخ احتياطية للتكوينات

💡 **تلميح:** انقر على أي تكوين لتحريره أو معاينته
"""
            
            self.bot.edit_message_text(
                text,
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown',
                reply_markup=keyboard
            )
            
        except Exception as e:
            logger.error(f"❌ خطأ في عرض إدارة الأزرار: {e}")
            self.bot.answer_callback_query(call.id, "❌ حدث خطأ")
    
    def show_button_templates(self, call):
        """عرض القوالب الجاهزة"""
        try:
            keyboard = types.InlineKeyboardMarkup(row_width=1)
            
            for template_key, template_info in self.button_templates.items():
                btn = types.InlineKeyboardButton(
                    f"{'⌨️' if template_info['type'] == 'keyboard' else '🔘'} {template_info['name']}",
                    callback_data=f"button_template_{template_key}"
                )
                keyboard.add(btn)
            
            btn_back = types.InlineKeyboardButton(
                "🔙 العودة لإدارة الأزرار",
                callback_data="admin_buttons"
            )
            keyboard.add(btn_back)
            
            text = """
📋 **القوالب الجاهزة للأزرار**

اختر القالب الذي تريد استخدامه:

⌨️ **كيبورد بسيط**
• أزرار كيبورد عادية للتنقل
• مناسب للقوائم الرئيسية
• سهل الاستخدام والتخصيص

🔘 **قائمة إنلاين**
• أزرار إنلاين للعمليات السريعة
• مناسب للخيارات والإعدادات
• يظهر أسفل الرسالة مباشرة

🧭 **أزرار التنقل**
• أزرار للتنقل بين الأقسام
• تتضمن السابق والتالي والرئيسية
• مناسب للمحتوى متعدد الصفحات

🌐 **روابط التواصل**
• أزرار للروابط الخارجية
• مناسب لوسائل التواصل الاجتماعي
• يفتح الروابط في متصفح خارجي

💡 **تلميح:** يمكنك تخصيص أي قالب بعد اختياره
"""
            
            self.bot.edit_message_text(
                text,
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown',
                reply_markup=keyboard
            )
            
        except Exception as e:
            logger.error(f"❌ خطأ في عرض القوالب: {e}")
            self.bot.answer_callback_query(call.id, "❌ حدث خطأ")
    
    def show_button_config_editor(self, call, config_name: str):
        """عرض محرر تكوين الأزرار"""
        try:
            config = button_manager.get_button_config(config_name)
            if not config:
                self.bot.answer_callback_query(call.id, "❌ التكوين غير موجود")
                return
            
            keyboard = types.InlineKeyboardMarkup(row_width=2)
            
            # أزرار إدارة التكوين
            btn_edit_buttons = types.InlineKeyboardButton(
                "✏️ تحرير الأزرار",
                callback_data=f"button_edit_buttons_{config_name}"
            )
            btn_add_button = types.InlineKeyboardButton(
                "➕ إضافة زر",
                callback_data=f"button_add_{config_name}"
            )
            btn_layout = types.InlineKeyboardButton(
                "🏗️ تعديل التخطيط",
                callback_data=f"button_layout_{config_name}"
            )
            btn_preview = types.InlineKeyboardButton(
                "👁️ معاينة",
                callback_data=f"button_preview_{config_name}"
            )
            btn_duplicate = types.InlineKeyboardButton(
                "📋 نسخ التكوين",
                callback_data=f"button_duplicate_{config_name}"
            )
            btn_delete = types.InlineKeyboardButton(
                "🗑️ حذف التكوين",
                callback_data=f"button_delete_{config_name}"
            )
            btn_back = types.InlineKeyboardButton(
                "🔙 العودة لإدارة الأزرار",
                callback_data="admin_buttons"
            )
            
            keyboard.add(btn_edit_buttons, btn_add_button)
            keyboard.add(btn_layout, btn_preview)
            keyboard.add(btn_duplicate, btn_delete)
            keyboard.add(btn_back)
            
            button_type = config.get("type", "keyboard")
            layout = config.get("layout", "غير محدد")
            buttons = config.get("buttons", [])
            
            text = f"""
🔘 **محرر التكوين: {config_name}**

📋 **معلومات التكوين:**
• النوع: {'⌨️ Keyboard' if button_type == 'keyboard' else '🔘 Inline'}
• التخطيط: {layout}
• عدد الأزرار: {len(buttons)}

🔘 **الأزرار الحالية:**
"""
            
            if buttons:
                for i, button in enumerate(buttons[:10], 1):  # عرض أول 10 أزرار
                    button_text = button.get("text", "بدون نص")
                    row = button.get("row", 1)
                    position = button.get("position", 1)
                    
                    text += f"{i}. **{button_text}**\n"
                    text += f"   الموضع: صف {row}, موقع {position}\n"
                    
                    if button_type == "keyboard":
                        action = button.get("action", "غير محدد")
                        target = button.get("target", "غير محدد")
                        text += f"   الإجراء: {action} → {target}\n\n"
                    else:
                        callback_data = button.get("callback_data", "")
                        url = button.get("url", "")
                        if url:
                            text += f"   الرابط: {url}\n\n"
                        else:
                            text += f"   البيانات: {callback_data}\n\n"
                
                if len(buttons) > 10:
                    text += f"... و {len(buttons) - 10} أزرار أخرى\n\n"
            else:
                text += "لا توجد أزرار في هذا التكوين\n\n"
            
            text += """
🎯 **العمليات المتاحة:**
• **تحرير الأزرار** - تعديل النصوص والإجراءات
• **إضافة زر** - إضافة زر جديد للتكوين
• **تعديل التخطيط** - تغيير ترتيب وتنظيم الأزرار
• **معاينة** - اختبار التكوين قبل النشر
• **نسخ التكوين** - إنشاء نسخة للتعديل
• **حذف التكوين** - حذف التكوين نهائياً

💡 **تلميح:** استخدم المعاينة للتأكد من صحة التكوين قبل النشر
"""
            
            self.bot.edit_message_text(
                text,
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown',
                reply_markup=keyboard
            )
            
        except Exception as e:
            logger.error(f"❌ خطأ في عرض محرر التكوين: {e}")
            self.bot.answer_callback_query(call.id, "❌ حدث خطأ")
    
    def preview_button_config(self, call, config_name: str):
        """معاينة تكوين الأزرار"""
        try:
            config = button_manager.get_button_config(config_name)
            if not config:
                self.bot.answer_callback_query(call.id, "❌ التكوين غير موجود")
                return
            
            # إنشاء الكيبورد للمعاينة
            preview_keyboard = button_manager.create_keyboard(config_name)
            
            # إضافة زر العودة
            back_keyboard = types.InlineKeyboardMarkup()
            btn_back = types.InlineKeyboardButton(
                "🔙 العودة للمحرر",
                callback_data=f"button_edit_config_{config_name}"
            )
            back_keyboard.add(btn_back)
            
            button_type = config.get("type", "keyboard")
            
            preview_text = f"""
👁️ **معاينة التكوين: {config_name}**

📋 **معلومات:**
• النوع: {'⌨️ Keyboard' if button_type == 'keyboard' else '🔘 Inline'}
• عدد الأزرار: {len(config.get('buttons', []))}

⬇️ **المعاينة أدناه:**

💡 **ملاحظة:** هذه معاينة تجريبية - الأزرار قد لا تعمل بشكل كامل
"""
            
            # إرسال رسالة المعاينة
            if button_type == "keyboard":
                self.bot.send_message(
                    call.message.chat.id,
                    "🔽 **معاينة الكيبورد:**",
                    reply_markup=preview_keyboard,
                    parse_mode='Markdown'
                )
            else:
                self.bot.send_message(
                    call.message.chat.id,
                    "🔽 **معاينة الأزرار الإنلاين:**",
                    reply_markup=preview_keyboard,
                    parse_mode='Markdown'
                )
            
            # تحديث الرسالة الأصلية
            self.bot.edit_message_text(
                preview_text,
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown',
                reply_markup=back_keyboard
            )
            
        except Exception as e:
            logger.error(f"❌ خطأ في معاينة التكوين: {e}")
            self.bot.answer_callback_query(call.id, "❌ حدث خطأ في المعاينة")
    
    def is_user_editing_buttons(self, user_id: int) -> bool:
        """التحقق من وجود جلسة تحرير أزرار نشطة للمستخدم"""
        for session in self.editing_sessions.values():
            if session["user_id"] == user_id:
                return True
        return False

# دالة مساعدة لإنشاء مثيل محرر الأزرار
def create_button_editor(bot):
    """إنشاء مثيل محرر الأزرار"""
    return ButtonEditor(bot)
