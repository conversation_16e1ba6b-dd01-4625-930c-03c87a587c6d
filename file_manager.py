#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير الملفات والوسائط للبوت التعليمي
File and Media Manager for Educational Bot

يدير رفع وتنظيم الملفات والوسائط
"""

import logging
import os
import hashlib
from typing import Dict, Any, Optional, List
from datetime import datetime
from firebase_manager import firebase_manager

logger = logging.getLogger(__name__)

class FileManager:
    """مدير الملفات والوسائط"""
    
    def __init__(self):
        self.files_data = {}
        self.upload_settings = {}
        self.load_files_from_firebase()
        self.setup_directories()
    
    def setup_directories(self):
        """إنشاء المجلدات المطلوبة"""
        try:
            directories = [
                "uploads",
                "uploads/documents",
                "uploads/images", 
                "uploads/videos",
                "uploads/audio",
                "uploads/educational",
                "uploads/ministerial",
                "uploads/services",
                "uploads/temp"
            ]
            
            for directory in directories:
                if not os.path.exists(directory):
                    os.makedirs(directory)
                    logger.info(f"✅ تم إنشاء المجلد: {directory}")
                    
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء المجلدات: {e}")
    
    def load_files_from_firebase(self):
        """تحميل بيانات الملفات من Firebase"""
        try:
            # سيتم تطوير هذا لاحقاً مع Firebase Storage
            self.files_data = self.get_default_files_structure()
            self.upload_settings = self.get_default_upload_settings()
            logger.info("✅ تم تحميل بيانات الملفات")
        except Exception as e:
            logger.error(f"❌ خطأ في تحميل بيانات الملفات: {e}")
            self.files_data = self.get_default_files_structure()
            self.upload_settings = self.get_default_upload_settings()
    
    def get_default_files_structure(self) -> Dict[str, Any]:
        """هيكل الملفات الافتراضي"""
        return {
            "structure": {
                "root": "uploads",
                "categories": {
                    "educational": {
                        "path": "uploads/educational",
                        "description": "ملفات المحتوى التعليمي",
                        "allowed_types": ["pdf", "doc", "docx", "ppt", "pptx", "txt"],
                        "max_size": 50  # MB
                    },
                    "ministerial": {
                        "path": "uploads/ministerial",
                        "description": "ملفات المواد الوزارية",
                        "allowed_types": ["pdf", "doc", "docx"],
                        "max_size": 25  # MB
                    },
                    "services": {
                        "path": "uploads/services",
                        "description": "ملفات الخدمات وأمثلة الأعمال",
                        "allowed_types": ["pdf", "jpg", "jpeg", "png", "gif", "mp4"],
                        "max_size": 100  # MB
                    },
                    "images": {
                        "path": "uploads/images",
                        "description": "الصور والرسوم التوضيحية",
                        "allowed_types": ["jpg", "jpeg", "png", "gif", "webp"],
                        "max_size": 10  # MB
                    },
                    "documents": {
                        "path": "uploads/documents",
                        "description": "المستندات العامة",
                        "allowed_types": ["pdf", "doc", "docx", "txt", "rtf"],
                        "max_size": 25  # MB
                    }
                }
            },
            "files": {},
            "statistics": {
                "total_files": 0,
                "total_size": 0,
                "files_by_type": {},
                "files_by_category": {}
            }
        }
    
    def get_default_upload_settings(self) -> Dict[str, Any]:
        """إعدادات الرفع الافتراضية"""
        return {
            "general": {
                "max_file_size": 50,  # MB
                "allowed_extensions": [
                    "pdf", "doc", "docx", "ppt", "pptx", "txt", "rtf",
                    "jpg", "jpeg", "png", "gif", "webp", "bmp",
                    "mp4", "avi", "mov", "wmv", "flv",
                    "mp3", "wav", "aac", "ogg"
                ],
                "scan_for_viruses": True,
                "auto_compress": True,
                "generate_thumbnails": True
            },
            "naming": {
                "use_original_name": False,
                "add_timestamp": True,
                "add_hash": True,
                "sanitize_names": True
            },
            "storage": {
                "local_storage": True,
                "cloud_storage": False,
                "backup_enabled": True,
                "auto_cleanup": True,
                "cleanup_days": 30
            },
            "access": {
                "public_access": False,
                "admin_only": False,
                "require_authentication": True,
                "download_tracking": True
            }
        }
    
    def validate_file(self, file_path: str, category: str = "documents") -> Dict[str, Any]:
        """التحقق من صحة الملف"""
        try:
            if not os.path.exists(file_path):
                return {"valid": False, "error": "الملف غير موجود"}
            
            # الحصول على معلومات الملف
            file_size = os.path.getsize(file_path)
            file_name = os.path.basename(file_path)
            file_ext = file_name.split('.')[-1].lower() if '.' in file_name else ""
            
            # التحقق من الفئة
            category_settings = self.files_data["structure"]["categories"].get(category, {})
            if not category_settings:
                return {"valid": False, "error": "فئة غير مدعومة"}
            
            # التحقق من نوع الملف
            allowed_types = category_settings.get("allowed_types", [])
            if file_ext not in allowed_types:
                return {"valid": False, "error": f"نوع الملف غير مدعوم. الأنواع المسموحة: {', '.join(allowed_types)}"}
            
            # التحقق من حجم الملف
            max_size_mb = category_settings.get("max_size", 50)
            max_size_bytes = max_size_mb * 1024 * 1024
            if file_size > max_size_bytes:
                return {"valid": False, "error": f"حجم الملف كبير جداً. الحد الأقصى: {max_size_mb} ميجابايت"}
            
            return {
                "valid": True,
                "file_info": {
                    "name": file_name,
                    "size": file_size,
                    "extension": file_ext,
                    "category": category
                }
            }
            
        except Exception as e:
            logger.error(f"❌ خطأ في التحقق من الملف: {e}")
            return {"valid": False, "error": "خطأ في التحقق من الملف"}
    
    def generate_file_hash(self, file_path: str) -> str:
        """إنشاء hash للملف"""
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء hash: {e}")
            return ""
    
    def generate_safe_filename(self, original_name: str, category: str = "documents") -> str:
        """إنشاء اسم ملف آمن"""
        try:
            # إزالة الأحرف الخطيرة
            safe_name = "".join(c for c in original_name if c.isalnum() or c in "._-")
            
            # إضافة timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # فصل الاسم والامتداد
            if '.' in safe_name:
                name_part, ext_part = safe_name.rsplit('.', 1)
                safe_name = f"{name_part}_{timestamp}.{ext_part}"
            else:
                safe_name = f"{safe_name}_{timestamp}"
            
            return safe_name
            
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء اسم الملف: {e}")
            return f"file_{datetime.now().timestamp()}"
    
    def save_file(self, file_path: str, category: str = "documents", 
                  metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """حفظ الملف"""
        try:
            # التحقق من صحة الملف
            validation = self.validate_file(file_path, category)
            if not validation["valid"]:
                return {"success": False, "error": validation["error"]}
            
            file_info = validation["file_info"]
            
            # إنشاء اسم ملف آمن
            safe_filename = self.generate_safe_filename(file_info["name"], category)
            
            # تحديد مسار الحفظ
            category_path = self.files_data["structure"]["categories"][category]["path"]
            destination_path = os.path.join(category_path, safe_filename)
            
            # نسخ الملف
            import shutil
            shutil.copy2(file_path, destination_path)
            
            # إنشاء hash للملف
            file_hash = self.generate_file_hash(destination_path)
            
            # إنشاء معرف فريد للملف
            file_id = f"{category}_{datetime.now().timestamp()}_{file_hash[:8]}"
            
            # حفظ معلومات الملف
            file_record = {
                "id": file_id,
                "original_name": file_info["name"],
                "safe_name": safe_filename,
                "path": destination_path,
                "category": category,
                "size": file_info["size"],
                "extension": file_info["extension"],
                "hash": file_hash,
                "upload_date": datetime.now().isoformat(),
                "metadata": metadata or {}
            }
            
            # حفظ في قاعدة البيانات
            self.files_data["files"][file_id] = file_record
            
            # تحديث الإحصائيات
            self._update_statistics(file_record)
            
            # حفظ في Firebase
            self._save_to_firebase()
            
            logger.info(f"✅ تم حفظ الملف: {safe_filename}")
            
            return {
                "success": True,
                "file_id": file_id,
                "file_record": file_record
            }
            
        except Exception as e:
            logger.error(f"❌ خطأ في حفظ الملف: {e}")
            return {"success": False, "error": "خطأ في حفظ الملف"}
    
    def get_file(self, file_id: str) -> Optional[Dict[str, Any]]:
        """الحصول على معلومات الملف"""
        return self.files_data["files"].get(file_id)
    
    def delete_file(self, file_id: str) -> bool:
        """حذف الملف"""
        try:
            file_record = self.get_file(file_id)
            if not file_record:
                return False
            
            # حذف الملف من النظام
            if os.path.exists(file_record["path"]):
                os.remove(file_record["path"])
            
            # حذف من قاعدة البيانات
            del self.files_data["files"][file_id]
            
            # تحديث الإحصائيات
            self._update_statistics_after_delete(file_record)
            
            # حفظ في Firebase
            self._save_to_firebase()
            
            logger.info(f"✅ تم حذف الملف: {file_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في حذف الملف: {e}")
            return False
    
    def get_files_by_category(self, category: str) -> List[Dict[str, Any]]:
        """الحصول على الملفات حسب الفئة"""
        try:
            files = []
            for file_id, file_record in self.files_data["files"].items():
                if file_record["category"] == category:
                    files.append(file_record)
            
            # ترتيب حسب تاريخ الرفع
            files.sort(key=lambda x: x["upload_date"], reverse=True)
            return files
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على الملفات: {e}")
            return []
    
    def search_files(self, query: str, category: Optional[str] = None) -> List[Dict[str, Any]]:
        """البحث في الملفات"""
        try:
            results = []
            query_lower = query.lower()
            
            for file_id, file_record in self.files_data["files"].items():
                # تصفية حسب الفئة إذا تم تحديدها
                if category and file_record["category"] != category:
                    continue
                
                # البحث في اسم الملف والوصف
                if (query_lower in file_record["original_name"].lower() or
                    query_lower in file_record.get("metadata", {}).get("description", "").lower()):
                    results.append(file_record)
            
            return results
            
        except Exception as e:
            logger.error(f"❌ خطأ في البحث: {e}")
            return []
    
    def _update_statistics(self, file_record: Dict[str, Any]):
        """تحديث الإحصائيات"""
        try:
            stats = self.files_data["statistics"]
            
            stats["total_files"] += 1
            stats["total_size"] += file_record["size"]
            
            # إحصائيات حسب النوع
            ext = file_record["extension"]
            if ext not in stats["files_by_type"]:
                stats["files_by_type"][ext] = 0
            stats["files_by_type"][ext] += 1
            
            # إحصائيات حسب الفئة
            category = file_record["category"]
            if category not in stats["files_by_category"]:
                stats["files_by_category"][category] = 0
            stats["files_by_category"][category] += 1
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحديث الإحصائيات: {e}")
    
    def _update_statistics_after_delete(self, file_record: Dict[str, Any]):
        """تحديث الإحصائيات بعد الحذف"""
        try:
            stats = self.files_data["statistics"]
            
            stats["total_files"] -= 1
            stats["total_size"] -= file_record["size"]
            
            # إحصائيات حسب النوع
            ext = file_record["extension"]
            if ext in stats["files_by_type"]:
                stats["files_by_type"][ext] -= 1
                if stats["files_by_type"][ext] <= 0:
                    del stats["files_by_type"][ext]
            
            # إحصائيات حسب الفئة
            category = file_record["category"]
            if category in stats["files_by_category"]:
                stats["files_by_category"][category] -= 1
                if stats["files_by_category"][category] <= 0:
                    del stats["files_by_category"][category]
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحديث الإحصائيات: {e}")
    
    def _save_to_firebase(self):
        """حفظ البيانات في Firebase"""
        try:
            # سيتم تطوير هذا لاحقاً
            pass
        except Exception as e:
            logger.error(f"❌ خطأ في حفظ البيانات: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """الحصول على الإحصائيات"""
        return self.files_data["statistics"].copy()
    
    def cleanup_old_files(self, days: int = 30) -> int:
        """تنظيف الملفات القديمة"""
        try:
            from datetime import timedelta
            cutoff_date = datetime.now() - timedelta(days=days)
            deleted_count = 0

            files_to_delete = []
            for file_id, file_record in self.files_data["files"].items():
                upload_date = datetime.fromisoformat(file_record["upload_date"])
                if upload_date < cutoff_date:
                    files_to_delete.append(file_id)

            for file_id in files_to_delete:
                if self.delete_file(file_id):
                    deleted_count += 1

            logger.info(f"✅ تم حذف {deleted_count} ملف قديم")
            return deleted_count

        except Exception as e:
            logger.error(f"❌ خطأ في تنظيف الملفات: {e}")
            return 0

    def create_folder(self, folder_name: str, parent_folder: str = None, category: str = "general") -> str:
        """إنشاء مجلد جديد"""
        try:
            folder_id = f"folder_{datetime.now().timestamp()}"
            folder_record = {
                "id": folder_id,
                "name": folder_name,
                "type": "folder",
                "category": category,
                "parent_folder": parent_folder,
                "created_date": datetime.now().isoformat(),
                "files_count": 0,
                "total_size": 0,
                "permissions": {
                    "read": True,
                    "write": True,
                    "delete": True
                }
            }

            self.files_data["files"][folder_id] = folder_record
            self._save_to_firebase()

            logger.info(f"✅ تم إنشاء المجلد: {folder_name}")
            return folder_id

        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء المجلد: {e}")
            return ""

    def move_file_to_folder(self, file_id: str, folder_id: str) -> bool:
        """نقل ملف إلى مجلد"""
        try:
            if file_id in self.files_data["files"] and folder_id in self.files_data["files"]:
                file_record = self.files_data["files"][file_id]
                folder_record = self.files_data["files"][folder_id]

                if folder_record.get("type") == "folder":
                    # تحديث مجلد الملف
                    old_folder = file_record.get("folder_id")
                    file_record["folder_id"] = folder_id

                    # تحديث إحصائيات المجلدات
                    if old_folder and old_folder in self.files_data["files"]:
                        old_folder_record = self.files_data["files"][old_folder]
                        old_folder_record["files_count"] = max(0, old_folder_record.get("files_count", 1) - 1)
                        old_folder_record["total_size"] = max(0, old_folder_record.get("total_size", file_record.get("size", 0)) - file_record.get("size", 0))

                    folder_record["files_count"] = folder_record.get("files_count", 0) + 1
                    folder_record["total_size"] = folder_record.get("total_size", 0) + file_record.get("size", 0)

                    self._save_to_firebase()
                    logger.info(f"✅ تم نقل الملف إلى المجلد")
                    return True
            return False
        except Exception as e:
            logger.error(f"❌ خطأ في نقل الملف: {e}")
            return False

    def get_folder_contents(self, folder_id: str = None) -> List[Dict[str, Any]]:
        """الحصول على محتويات مجلد"""
        try:
            contents = []
            for file_id, file_record in self.files_data["files"].items():
                if file_record.get("folder_id") == folder_id or (folder_id is None and not file_record.get("folder_id")):
                    contents.append(file_record)

            # ترتيب المجلدات أولاً ثم الملفات
            contents.sort(key=lambda x: (x.get("type") != "folder", x.get("upload_date", "")), reverse=True)
            return contents
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على محتويات المجلد: {e}")
            return []

    def compress_file(self, file_id: str) -> bool:
        """ضغط ملف (محاكاة)"""
        try:
            if file_id in self.files_data["files"]:
                file_record = self.files_data["files"][file_id]
                original_size = file_record.get("size", 0)
                compressed_size = int(original_size * 0.7)  # افتراض ضغط 30%

                file_record["compressed"] = True
                file_record["original_size"] = original_size
                file_record["size"] = compressed_size
                file_record["compression_ratio"] = round((1 - compressed_size/original_size) * 100, 1)

                self._save_to_firebase()
                logger.info(f"✅ تم ضغط الملف بنسبة {file_record['compression_ratio']}%")
                return True
            return False
        except Exception as e:
            logger.error(f"❌ خطأ في ضغط الملف: {e}")
            return False

    def create_thumbnail(self, file_id: str) -> bool:
        """إنشاء صورة مصغرة (محاكاة)"""
        try:
            if file_id in self.files_data["files"]:
                file_record = self.files_data["files"][file_id]
                file_type = file_record.get("extension", "").lower()

                if file_type in [".jpg", ".jpeg", ".png", ".gif", ".bmp"]:
                    # إنشاء صورة مصغرة للصور
                    thumbnail_id = f"thumb_{file_id}"
                    file_record["thumbnail_id"] = thumbnail_id
                    file_record["has_thumbnail"] = True

                    self._save_to_firebase()
                    logger.info(f"✅ تم إنشاء صورة مصغرة للملف")
                    return True
            return False
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء الصورة المصغرة: {e}")
            return False

    def get_advanced_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات متقدمة"""
        try:
            files = [f for f in self.files_data["files"].values() if f.get("type") != "folder"]
            folders = [f for f in self.files_data["files"].values() if f.get("type") == "folder"]

            total_size = sum(f.get("size", 0) for f in files)
            compressed_files = [f for f in files if f.get("compressed", False)]
            total_compression_saved = sum(f.get("original_size", 0) - f.get("size", 0) for f in compressed_files)

            # إحصائيات حسب النوع
            type_stats = {}
            for file_record in files:
                ext = file_record.get("extension", "unknown")
                if ext not in type_stats:
                    type_stats[ext] = {"count": 0, "size": 0}
                type_stats[ext]["count"] += 1
                type_stats[ext]["size"] += file_record.get("size", 0)

            # إحصائيات حسب الفئة
            category_stats = {}
            for file_record in files:
                category = file_record.get("category", "uncategorized")
                if category not in category_stats:
                    category_stats[category] = {"count": 0, "size": 0}
                category_stats[category]["count"] += 1
                category_stats[category]["size"] += file_record.get("size", 0)

            return {
                "total_files": len(files),
                "total_folders": len(folders),
                "total_size": total_size,
                "total_size_mb": round(total_size / (1024 * 1024), 2),
                "compressed_files": len(compressed_files),
                "compression_saved_mb": round(total_compression_saved / (1024 * 1024), 2),
                "files_with_thumbnails": len([f for f in files if f.get("has_thumbnail", False)]),
                "type_stats": type_stats,
                "category_stats": category_stats,
                "last_updated": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"❌ خطأ في حساب الإحصائيات المتقدمة: {e}")
            return {}

    def export_file_list(self, category: str = None) -> Dict[str, Any]:
        """تصدير قائمة الملفات"""
        try:
            files_list = []
            for file_id, file_record in self.files_data["files"].items():
                if file_record.get("type") == "folder":
                    continue
                if category is None or file_record.get("category") == category:
                    files_list.append({
                        "id": file_id,
                        "name": file_record.get("original_name", ""),
                        "extension": file_record.get("extension", ""),
                        "size": file_record.get("size", 0),
                        "category": file_record.get("category", ""),
                        "upload_date": file_record.get("upload_date", ""),
                        "compressed": file_record.get("compressed", False),
                        "has_thumbnail": file_record.get("has_thumbnail", False)
                    })

            export_data = {
                "export_date": datetime.now().isoformat(),
                "total_files": len(files_list),
                "category_filter": category,
                "files": files_list
            }

            return export_data
        except Exception as e:
            logger.error(f"❌ خطأ في تصدير قائمة الملفات: {e}")
            return {}

# إنشاء مثيل مدير الملفات
file_manager = FileManager()
