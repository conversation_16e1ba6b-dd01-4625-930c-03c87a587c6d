#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
محرر الأسئلة التفاعلي للبوت التعليمي
Interactive Question Editor for Educational Bot

يوفر واجهة تفاعلية لإضافة وتحرير أسئلة MCQ
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
from telebot import types
from mcq_manager import mcq_manager

logger = logging.getLogger(__name__)

class QuestionEditor:
    """محرر الأسئلة التفاعلي"""
    
    def __init__(self, bot):
        self.bot = bot
        self.editing_sessions = {}  # جلسات تحرير الأسئلة النشطة
        self.question_templates = self.get_question_templates()
    
    def get_question_templates(self) -> Dict[str, Any]:
        """قوالب الأسئلة المتاحة"""
        return {
            "basic_mcq": {
                "name": "سؤال اختيار من متعدد أساسي",
                "description": "سؤال مع 4 خيارات وإجابة واحدة صحيحة",
                "template": {
                    "question": "",
                    "options": ["", "", "", ""],
                    "correct_answer": 0,
                    "explanation": "",
                    "difficulty": "متوسط",
                    "category": "",
                    "year": str(datetime.now().year)
                }
            },
            "true_false": {
                "name": "سؤال صح أم خطأ",
                "description": "سؤال بخيارين: صح أو خطأ",
                "template": {
                    "question": "",
                    "options": ["صح", "خطأ"],
                    "correct_answer": 0,
                    "explanation": "",
                    "difficulty": "سهل",
                    "category": "",
                    "year": str(datetime.now().year)
                }
            },
            "multiple_choice_5": {
                "name": "سؤال اختيار من متعدد (5 خيارات)",
                "description": "سؤال مع 5 خيارات وإجابة واحدة صحيحة",
                "template": {
                    "question": "",
                    "options": ["", "", "", "", ""],
                    "correct_answer": 0,
                    "explanation": "",
                    "difficulty": "صعب",
                    "category": "",
                    "year": str(datetime.now().year)
                }
            }
        }
    
    def show_question_templates(self, call, subject_name: str):
        """عرض قوالب الأسئلة المتاحة"""
        try:
            keyboard = types.InlineKeyboardMarkup(row_width=1)
            
            for template_key, template_info in self.question_templates.items():
                btn = types.InlineKeyboardButton(
                    f"📝 {template_info['name']}",
                    callback_data=f"question_template_{subject_name}_{template_key}"
                )
                keyboard.add(btn)
            
            # أزرار إضافية
            btn_import = types.InlineKeyboardButton(
                "📥 استيراد من ملف",
                callback_data=f"question_import_{subject_name}"
            )
            btn_bulk_add = types.InlineKeyboardButton(
                "📋 إضافة متعددة",
                callback_data=f"question_bulk_{subject_name}"
            )
            btn_back = types.InlineKeyboardButton(
                f"🔙 العودة لإدارة {subject_name}",
                callback_data=f"admin_mcq_subject_{subject_name}"
            )
            
            keyboard.add(btn_import)
            keyboard.add(btn_bulk_add)
            keyboard.add(btn_back)
            
            text = f"""
📝 **إضافة سؤال جديد - {subject_name}**

اختر نوع السؤال الذي تريد إضافته:

🎯 **أنواع الأسئلة المتاحة:**

📝 **سؤال اختيار من متعدد أساسي**
• 4 خيارات مع إجابة واحدة صحيحة
• مناسب لمعظم الأسئلة الأكاديمية

✅ **سؤال صح أم خطأ**
• خيارين فقط: صح أو خطأ
• مناسب للمفاهيم الأساسية

🔢 **سؤال اختيار من متعدد (5 خيارات)**
• 5 خيارات مع إجابة واحدة صحيحة
• مناسب للأسئلة المعقدة

📥 **استيراد من ملف**
• تحميل أسئلة من ملف Excel أو JSON
• مناسب لإضافة عدد كبير من الأسئلة

📋 **إضافة متعددة**
• إضافة عدة أسئلة في جلسة واحدة
• مناسب للإعداد السريع

💡 **تلميح:** يمكنك تحرير أي سؤال بعد إضافته من قائمة الأسئلة
"""
            
            self.bot.edit_message_text(
                text,
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown',
                reply_markup=keyboard
            )
            
        except Exception as e:
            logger.error(f"❌ خطأ في عرض قوالب الأسئلة: {e}")
            self.bot.answer_callback_query(call.id, "❌ حدث خطأ")
    
    def start_question_creation(self, call, subject_name: str, template_key: str):
        """بدء إنشاء سؤال جديد"""
        try:
            user_id = call.from_user.id
            
            if template_key not in self.question_templates:
                self.bot.answer_callback_query(call.id, "❌ قالب غير موجود")
                return
            
            template_info = self.question_templates[template_key]
            question_template = template_info["template"].copy()
            
            # إنشاء جلسة تحرير
            session_id = f"{user_id}_{subject_name}_{datetime.now().timestamp()}"
            self.editing_sessions[session_id] = {
                "user_id": user_id,
                "subject_name": subject_name,
                "template_key": template_key,
                "chat_id": call.message.chat.id,
                "message_id": call.message.message_id,
                "question_data": question_template,
                "current_step": "question",
                "start_time": datetime.now().isoformat()
            }
            
            # بدء عملية إدخال السؤال
            self.show_question_input_step(session_id)
            
        except Exception as e:
            logger.error(f"❌ خطأ في بدء إنشاء السؤال: {e}")
            self.bot.answer_callback_query(call.id, "❌ حدث خطأ")
    
    def show_question_input_step(self, session_id: str):
        """عرض خطوة إدخال السؤال"""
        try:
            session = self.editing_sessions.get(session_id)
            if not session:
                return
            
            current_step = session["current_step"]
            question_data = session["question_data"]
            template_key = session["template_key"]
            template_info = self.question_templates[template_key]
            
            keyboard = types.InlineKeyboardMarkup()
            btn_cancel = types.InlineKeyboardButton(
                "❌ إلغاء",
                callback_data=f"question_cancel_{session_id}"
            )
            keyboard.add(btn_cancel)
            
            if current_step == "question":
                text = f"""
📝 **إنشاء سؤال جديد**

🎯 **نوع السؤال:** {template_info['name']}
📚 **المادة:** {session['subject_name']}

📋 **الخطوة 1/6: نص السؤال**

💬 **اكتب نص السؤال الآن:**

💡 **تلميحات:**
• اكتب السؤال بوضوح ودقة
• تجنب الأسئلة الغامضة أو المعقدة
• يمكن استخدام الرموز والأرقام
• أقصى طول: 500 حرف

📝 **مثال:**
"ما هي وحدة قياس الجرعة الإشعاعية الممتصة؟"
"""
            
            elif current_step == "options":
                options_count = len(question_data["options"])
                current_option = session.get("current_option", 0)
                
                text = f"""
📝 **إنشاء سؤال جديد**

📋 **الخطوة 2/6: الخيارات**

✅ **السؤال:** {question_data['question']}

🔢 **الخيار {current_option + 1}/{options_count}:**

💬 **اكتب نص الخيار الآن:**

💡 **تلميحات:**
• اكتب خيارات واضحة ومختصرة
• تجنب الخيارات المتشابهة
• رتب الخيارات منطقياً إذا أمكن
• أقصى طول للخيار: 200 حرف

📝 **مثال:**
"جراي (Gy)"
"""
            
            elif current_step == "correct_answer":
                text = f"""
📝 **إنشاء سؤال جديد**

📋 **الخطوة 3/6: الإجابة الصحيحة**

✅ **السؤال:** {question_data['question']}

🔢 **الخيارات:**
"""
                for i, option in enumerate(question_data["options"]):
                    text += f"{i + 1}. {option}\n"
                
                text += f"""
💬 **اكتب رقم الإجابة الصحيحة (1-{len(question_data['options'])}):**

💡 **تلميح:** اكتب الرقم فقط (مثل: 1 أو 2 أو 3)
"""
            
            elif current_step == "explanation":
                correct_option = question_data["options"][question_data["correct_answer"]]
                text = f"""
📝 **إنشاء سؤال جديد**

📋 **الخطوة 4/6: شرح الإجابة**

✅ **السؤال:** {question_data['question']}
🎯 **الإجابة الصحيحة:** {correct_option}

💬 **اكتب شرح الإجابة الصحيحة:**

💡 **تلميحات:**
• اشرح لماذا هذه الإجابة صحيحة
• يمكن ذكر لماذا الخيارات الأخرى خاطئة
• استخدم مصطلحات علمية دقيقة
• أقصى طول: 1000 حرف

📝 **مثال:**
"الجراي (Gy) هي وحدة قياس الجرعة الإشعاعية الممتصة، بينما السيفرت يقيس الجرعة المكافئة"
"""
            
            elif current_step == "difficulty":
                text = f"""
📝 **إنشاء سؤال جديد**

📋 **الخطوة 5/6: مستوى الصعوبة**

💬 **اختر مستوى صعوبة السؤال:**

🟢 **سهل** - للمفاهيم الأساسية
🟡 **متوسط** - للمفاهيم العامة
🔴 **صعب** - للمفاهيم المتقدمة

💡 **تلميح:** مستوى الصعوبة يساعد في تنظيم الأسئلة وإنشاء اختبارات متدرجة
"""
                
                # إضافة أزرار اختيار الصعوبة
                keyboard = types.InlineKeyboardMarkup(row_width=3)
                btn_easy = types.InlineKeyboardButton("🟢 سهل", callback_data=f"question_difficulty_{session_id}_سهل")
                btn_medium = types.InlineKeyboardButton("🟡 متوسط", callback_data=f"question_difficulty_{session_id}_متوسط")
                btn_hard = types.InlineKeyboardButton("🔴 صعب", callback_data=f"question_difficulty_{session_id}_صعب")
                btn_cancel = types.InlineKeyboardButton("❌ إلغاء", callback_data=f"question_cancel_{session_id}")
                
                keyboard.add(btn_easy, btn_medium, btn_hard)
                keyboard.add(btn_cancel)
            
            elif current_step == "category":
                text = f"""
📝 **إنشاء سؤال جديد**

📋 **الخطوة 6/6: فئة السؤال**

💬 **اكتب فئة أو موضوع السؤال:**

💡 **أمثلة على الفئات:**
• أجهزة التصوير
• الحماية الإشعاعية
• الفيزياء النووية
• التشريح العام
• علم الأمراض

📝 **تلميح:** الفئة تساعد في تنظيم الأسئلة وإنشاء اختبارات موضوعية
"""
            
            self.bot.edit_message_text(
                text,
                session["chat_id"],
                session["message_id"],
                parse_mode='Markdown',
                reply_markup=keyboard
            )
            
        except Exception as e:
            logger.error(f"❌ خطأ في عرض خطوة إدخال السؤال: {e}")
    
    def handle_question_input(self, message):
        """معالجة إدخال بيانات السؤال"""
        try:
            user_id = message.from_user.id
            
            # البحث عن جلسة تحرير نشطة
            active_session = None
            session_id = None
            for sid, session in self.editing_sessions.items():
                if session["user_id"] == user_id:
                    active_session = session
                    session_id = sid
                    break
            
            if not active_session:
                return False  # لا توجد جلسة تحرير نشطة
            
            input_text = message.text.strip()
            current_step = active_session["current_step"]
            question_data = active_session["question_data"]
            
            # التحقق من إلغاء التحرير
            if input_text.lower() in ["إلغاء", "cancel", "الغاء"]:
                self.cancel_question_editing(active_session)
                return True
            
            # معالجة الإدخال حسب الخطوة الحالية
            if current_step == "question":
                if len(input_text) > 500:
                    self.bot.send_message(
                        message.chat.id,
                        "❌ **نص السؤال طويل جداً**\n\nالحد الأقصى: 500 حرف\nالطول الحالي: {} حرف\n\nيرجى اختصار السؤال وإعادة المحاولة.".format(len(input_text)),
                        parse_mode='Markdown'
                    )
                    return True
                
                question_data["question"] = input_text
                active_session["current_step"] = "options"
                active_session["current_option"] = 0
                
            elif current_step == "options":
                if len(input_text) > 200:
                    self.bot.send_message(
                        message.chat.id,
                        "❌ **نص الخيار طويل جداً**\n\nالحد الأقصى: 200 حرف\nالطول الحالي: {} حرف\n\nيرجى اختصار الخيار وإعادة المحاولة.".format(len(input_text)),
                        parse_mode='Markdown'
                    )
                    return True
                
                current_option = active_session.get("current_option", 0)
                question_data["options"][current_option] = input_text
                
                # الانتقال للخيار التالي أو للخطوة التالية
                if current_option + 1 < len(question_data["options"]):
                    active_session["current_option"] = current_option + 1
                else:
                    active_session["current_step"] = "correct_answer"
                    del active_session["current_option"]
                
            elif current_step == "correct_answer":
                try:
                    answer_num = int(input_text)
                    if 1 <= answer_num <= len(question_data["options"]):
                        question_data["correct_answer"] = answer_num - 1
                        active_session["current_step"] = "explanation"
                    else:
                        self.bot.send_message(
                            message.chat.id,
                            f"❌ **رقم غير صحيح**\n\nيرجى إدخال رقم بين 1 و {len(question_data['options'])}"
                        )
                        return True
                except ValueError:
                    self.bot.send_message(
                        message.chat.id,
                        "❌ **يرجى إدخال رقم صحيح**\n\nمثال: 1 أو 2 أو 3"
                    )
                    return True
                
            elif current_step == "explanation":
                if len(input_text) > 1000:
                    self.bot.send_message(
                        message.chat.id,
                        "❌ **شرح الإجابة طويل جداً**\n\nالحد الأقصى: 1000 حرف\nالطول الحالي: {} حرف\n\nيرجى اختصار الشرح وإعادة المحاولة.".format(len(input_text)),
                        parse_mode='Markdown'
                    )
                    return True
                
                question_data["explanation"] = input_text
                active_session["current_step"] = "difficulty"
                
            elif current_step == "category":
                question_data["category"] = input_text
                # إنهاء عملية إنشاء السؤال
                self.finalize_question_creation(session_id)
                return True
            
            # عرض الخطوة التالية
            self.show_question_input_step(session_id)
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في معالجة إدخال السؤال: {e}")
            return False
    
    def handle_difficulty_selection(self, call, session_id: str, difficulty: str):
        """معالجة اختيار مستوى الصعوبة"""
        try:
            session = self.editing_sessions.get(session_id)
            if not session:
                self.bot.answer_callback_query(call.id, "❌ جلسة منتهية الصلاحية")
                return
            
            session["question_data"]["difficulty"] = difficulty
            session["current_step"] = "category"
            
            self.bot.answer_callback_query(call.id, f"✅ تم اختيار: {difficulty}")
            self.show_question_input_step(session_id)
            
        except Exception as e:
            logger.error(f"❌ خطأ في اختيار الصعوبة: {e}")
            self.bot.answer_callback_query(call.id, "❌ حدث خطأ")
    
    def finalize_question_creation(self, session_id: str):
        """إنهاء عملية إنشاء السؤال"""
        try:
            session = self.editing_sessions.get(session_id)
            if not session:
                return
            
            question_data = session["question_data"]
            subject_name = session["subject_name"]
            
            # إضافة السؤال إلى قاعدة البيانات
            success = mcq_manager.add_question(subject_name, question_data)
            
            if success:
                # إرسال رسالة تأكيد
                keyboard = types.InlineKeyboardMarkup()
                btn_add_another = types.InlineKeyboardButton(
                    "➕ إضافة سؤال آخر",
                    callback_data=f"admin_mcq_add_{subject_name}"
                )
                btn_view_questions = types.InlineKeyboardButton(
                    "📋 عرض الأسئلة",
                    callback_data=f"admin_mcq_view_{subject_name}"
                )
                btn_back = types.InlineKeyboardButton(
                    f"🔙 العودة لإدارة {subject_name}",
                    callback_data=f"admin_mcq_subject_{subject_name}"
                )
                
                keyboard.add(btn_add_another)
                keyboard.add(btn_view_questions)
                keyboard.add(btn_back)
                
                correct_option = question_data["options"][question_data["correct_answer"]]
                
                success_text = f"""
✅ **تم إضافة السؤال بنجاح!**

📚 **المادة:** {subject_name}
📝 **السؤال:** {question_data['question']}
🎯 **الإجابة الصحيحة:** {correct_option}
📊 **الصعوبة:** {question_data['difficulty']}
📂 **الفئة:** {question_data['category']}

💾 **تم حفظ السؤال في قاعدة البيانات**

🎯 **ماذا تريد أن تفعل الآن؟**
"""
                
                self.bot.edit_message_text(
                    success_text,
                    session["chat_id"],
                    session["message_id"],
                    parse_mode='Markdown',
                    reply_markup=keyboard
                )
                
            else:
                self.bot.edit_message_text(
                    "❌ **فشل في إضافة السؤال**\n\nحدث خطأ أثناء حفظ السؤال. يرجى المحاولة مرة أخرى.",
                    session["chat_id"],
                    session["message_id"],
                    parse_mode='Markdown'
                )
            
            # حذف جلسة التحرير
            del self.editing_sessions[session_id]
            
        except Exception as e:
            logger.error(f"❌ خطأ في إنهاء إنشاء السؤال: {e}")
    
    def cancel_question_editing(self, session):
        """إلغاء تحرير السؤال"""
        try:
            keyboard = types.InlineKeyboardMarkup()
            btn_back = types.InlineKeyboardButton(
                f"🔙 العودة لإدارة {session['subject_name']}",
                callback_data=f"admin_mcq_subject_{session['subject_name']}"
            )
            keyboard.add(btn_back)
            
            self.bot.edit_message_text(
                "❌ **تم إلغاء إنشاء السؤال**\n\nلم يتم حفظ أي بيانات.",
                session["chat_id"],
                session["message_id"],
                parse_mode='Markdown',
                reply_markup=keyboard
            )
            
        except Exception as e:
            logger.error(f"❌ خطأ في إلغاء تحرير السؤال: {e}")
    
    def is_user_editing_question(self, user_id: int) -> bool:
        """التحقق من وجود جلسة تحرير سؤال نشطة للمستخدم"""
        for session in self.editing_sessions.values():
            if session["user_id"] == user_id:
                return True
        return False

# دالة مساعدة لإنشاء مثيل محرر الأسئلة
def create_question_editor(bot):
    """إنشاء مثيل محرر الأسئلة"""
    return QuestionEditor(bot)
