#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير أسئلة MCQ للبوت التعليمي
MCQ Manager for Educational Bot

يدير إنشاء وتخصيص الأسئلة متعددة الخيارات
"""

import logging
import random
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime
from firebase_manager import firebase_manager

logger = logging.getLogger(__name__)

class MCQManager:
    """مدير أسئلة MCQ"""
    
    def __init__(self):
        self.questions_data = {}
        self.test_settings = {}
        self.load_questions_from_firebase()
    
    def load_questions_from_firebase(self):
        """تحميل الأسئلة من Firebase"""
        try:
            self.questions_data = firebase_manager.get_mcq_questions()
            if not self.questions_data:
                self.questions_data = self.get_default_questions()
                firebase_manager.save_mcq_questions(self.questions_data)
            logger.info("✅ تم تحميل أسئلة MCQ من Firebase")
        except Exception as e:
            logger.error(f"❌ خطأ في تحميل أسئلة MCQ: {e}")
            self.questions_data = self.get_default_questions()
    
    def get_default_questions(self) -> Dict[str, Any]:
        """الأسئلة الافتراضية"""
        return {
            "subjects": {
                "الفيزياء الطبية": {
                    "settings": {
                        "questions_per_test": 10,
                        "time_limit": 15,  # بالدقائق
                        "shuffle_questions": True,
                        "shuffle_answers": True,
                        "show_correct_answer": True,
                        "show_explanation": True,
                        "passing_score": 60  # النسبة المئوية للنجاح
                    },
                    "questions": [
                        {
                            "id": "phys_001",
                            "question": "ما هو مبدأ عمل جهاز الأشعة السينية؟",
                            "options": [
                                "انبعاث الإلكترونات من الكاثود وضربها للأنود",
                                "التأين الإشعاعي للمواد",
                                "الرنين المغناطيسي للذرات",
                                "الموجات فوق الصوتية عالية التردد"
                            ],
                            "correct_answer": 0,
                            "explanation": "جهاز الأشعة السينية يعمل على مبدأ انبعاث الإلكترونات من الكاثود وضربها للأنود لإنتاج الأشعة السينية",
                            "difficulty": "متوسط",
                            "category": "أجهزة التصوير",
                            "year": "2023"
                        },
                        {
                            "id": "phys_002",
                            "question": "ما هي وحدة قياس الجرعة الإشعاعية الممتصة؟",
                            "options": [
                                "جراي (Gy)",
                                "سيفرت (Sv)",
                                "بيكريل (Bq)",
                                "كوري (Ci)"
                            ],
                            "correct_answer": 0,
                            "explanation": "الجراي (Gy) هي وحدة قياس الجرعة الإشعاعية الممتصة، بينما السيفرت يقيس الجرعة المكافئة",
                            "difficulty": "سهل",
                            "category": "الحماية الإشعاعية",
                            "year": "2023"
                        },
                        {
                            "id": "phys_003",
                            "question": "ما هو الغرض من استخدام المرشحات في أجهزة الأشعة السينية؟",
                            "options": [
                                "زيادة شدة الإشعاع",
                                "تحسين جودة الصورة وتقليل الجرعة",
                                "تسريع عملية التصوير",
                                "تقليل تكلفة التشغيل"
                            ],
                            "correct_answer": 1,
                            "explanation": "المرشحات تزيل الأشعة السينية منخفضة الطاقة مما يحسن جودة الصورة ويقلل الجرعة على المريض",
                            "difficulty": "متوسط",
                            "category": "أجهزة التصوير",
                            "year": "2022"
                        }
                    ]
                },
                "التشريح": {
                    "settings": {
                        "questions_per_test": 8,
                        "time_limit": 12,
                        "shuffle_questions": True,
                        "shuffle_answers": True,
                        "show_correct_answer": True,
                        "show_explanation": True,
                        "passing_score": 70
                    },
                    "questions": [
                        {
                            "id": "anat_001",
                            "question": "كم عدد العظام في جسم الإنسان البالغ؟",
                            "options": [
                                "206 عظمة",
                                "208 عظمة",
                                "210 عظمة",
                                "204 عظمة"
                            ],
                            "correct_answer": 0,
                            "explanation": "جسم الإنسان البالغ يحتوي على 206 عظمة، بينما الطفل يولد بحوالي 270 عظمة تلتحم مع النمو",
                            "difficulty": "سهل",
                            "category": "الجهاز العظمي",
                            "year": "2023"
                        }
                    ]
                }
            },
            "test_history": {},
            "statistics": {
                "total_tests": 0,
                "total_questions": 0,
                "average_score": 0
            }
        }
    
    def get_subjects(self) -> List[str]:
        """الحصول على قائمة المواد المتاحة"""
        return list(self.questions_data.get("subjects", {}).keys())
    
    def get_subject_info(self, subject: str) -> Optional[Dict[str, Any]]:
        """الحصول على معلومات مادة محددة"""
        subjects = self.questions_data.get("subjects", {})
        return subjects.get(subject)
    
    def create_test(self, subject: str, user_id: int, custom_settings: Optional[Dict[str, Any]] = None) -> Optional[Dict[str, Any]]:
        """إنشاء اختبار جديد"""
        try:
            subject_data = self.get_subject_info(subject)
            if not subject_data:
                logger.error(f"❌ المادة غير موجودة: {subject}")
                return None
            
            settings = subject_data.get("settings", {})
            if custom_settings:
                settings.update(custom_settings)
            
            all_questions = subject_data.get("questions", [])
            if not all_questions:
                logger.error(f"❌ لا توجد أسئلة للمادة: {subject}")
                return None
            
            # تحديد عدد الأسئلة
            questions_count = min(
                settings.get("questions_per_test", 10),
                len(all_questions)
            )
            
            # اختيار الأسئلة
            if settings.get("shuffle_questions", True):
                selected_questions = random.sample(all_questions, questions_count)
            else:
                selected_questions = all_questions[:questions_count]
            
            # خلط الإجابات إذا كان مطلوباً
            if settings.get("shuffle_answers", True):
                selected_questions = self._shuffle_answers(selected_questions)
            
            # إنشاء معرف الاختبار
            test_id = f"{subject}_{user_id}_{int(datetime.now().timestamp())}"
            
            test_data = {
                "test_id": test_id,
                "subject": subject,
                "user_id": user_id,
                "questions": selected_questions,
                "settings": settings,
                "created_at": datetime.now().isoformat(),
                "status": "active",
                "current_question": 0,
                "answers": {},
                "score": 0,
                "start_time": datetime.now().isoformat()
            }
            
            logger.info(f"✅ تم إنشاء اختبار جديد: {test_id}")
            return test_data
            
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء الاختبار: {e}")
            return None
    
    def _shuffle_answers(self, questions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """خلط ترتيب الإجابات"""
        shuffled_questions = []
        
        for question in questions:
            question_copy = question.copy()
            options = question_copy["options"].copy()
            correct_answer = question_copy["correct_answer"]
            
            # إنشاء قائمة مفاتيح مع الإجابات
            indexed_options = list(enumerate(options))
            random.shuffle(indexed_options)
            
            # إعادة ترتيب الخيارات وتحديث الإجابة الصحيحة
            new_options = []
            new_correct_answer = 0
            
            for new_index, (old_index, option) in enumerate(indexed_options):
                new_options.append(option)
                if old_index == correct_answer:
                    new_correct_answer = new_index
            
            question_copy["options"] = new_options
            question_copy["correct_answer"] = new_correct_answer
            question_copy["original_order"] = [idx for idx, _ in indexed_options]
            
            shuffled_questions.append(question_copy)
        
        return shuffled_questions
    
    def get_question(self, test_data: Dict[str, Any], question_index: int) -> Optional[Dict[str, Any]]:
        """الحصول على سؤال محدد من الاختبار"""
        try:
            questions = test_data.get("questions", [])
            if 0 <= question_index < len(questions):
                return questions[question_index]
            return None
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على السؤال: {e}")
            return None
    
    def submit_answer(self, test_data: Dict[str, Any], question_index: int, answer: int) -> bool:
        """تسجيل إجابة المستخدم"""
        try:
            test_data["answers"][str(question_index)] = answer
            return True
        except Exception as e:
            logger.error(f"❌ خطأ في تسجيل الإجابة: {e}")
            return False
    
    def calculate_score(self, test_data: Dict[str, Any]) -> Dict[str, Any]:
        """حساب نتيجة الاختبار"""
        try:
            questions = test_data.get("questions", [])
            answers = test_data.get("answers", {})
            
            correct_count = 0
            total_questions = len(questions)
            detailed_results = []
            
            for i, question in enumerate(questions):
                user_answer = answers.get(str(i))
                correct_answer = question["correct_answer"]
                is_correct = user_answer == correct_answer
                
                if is_correct:
                    correct_count += 1
                
                detailed_results.append({
                    "question_id": question.get("id", f"q_{i}"),
                    "question": question["question"],
                    "user_answer": user_answer,
                    "correct_answer": correct_answer,
                    "is_correct": is_correct,
                    "explanation": question.get("explanation", "")
                })
            
            score_percentage = (correct_count / total_questions) * 100 if total_questions > 0 else 0
            passing_score = test_data.get("settings", {}).get("passing_score", 60)
            
            # تحديد التقدير
            if score_percentage >= 90:
                grade = "ممتاز"
            elif score_percentage >= 80:
                grade = "جيد جداً"
            elif score_percentage >= 70:
                grade = "جيد"
            elif score_percentage >= passing_score:
                grade = "مقبول"
            else:
                grade = "راسب"
            
            result = {
                "correct_count": correct_count,
                "total_questions": total_questions,
                "score_percentage": round(score_percentage, 2),
                "grade": grade,
                "passed": score_percentage >= passing_score,
                "detailed_results": detailed_results,
                "completion_time": datetime.now().isoformat()
            }
            
            # حفظ النتيجة
            test_data["score"] = score_percentage
            test_data["result"] = result
            test_data["status"] = "completed"
            test_data["end_time"] = datetime.now().isoformat()
            
            # حفظ في Firebase
            firebase_manager.save_test_result(test_data["user_id"], {
                "test_id": test_data["test_id"],
                "subject": test_data["subject"],
                "score": score_percentage,
                "grade": grade,
                "passed": result["passed"],
                "questions_count": total_questions,
                "correct_count": correct_count
            })
            
            return result
            
        except Exception as e:
            logger.error(f"❌ خطأ في حساب النتيجة: {e}")
            return {}
    
    def add_question(self, subject: str, question_data: Dict[str, Any]) -> bool:
        """إضافة سؤال جديد"""
        try:
            if subject not in self.questions_data.get("subjects", {}):
                self.questions_data["subjects"][subject] = {
                    "settings": self.get_default_settings(),
                    "questions": []
                }
            
            # إضافة معرف فريد للسؤال
            if "id" not in question_data:
                question_data["id"] = f"{subject.lower()}_{len(self.questions_data['subjects'][subject]['questions']) + 1:03d}"
            
            self.questions_data["subjects"][subject]["questions"].append(question_data)
            
            # حفظ في Firebase
            success = firebase_manager.save_mcq_questions(self.questions_data)
            if success:
                logger.info(f"✅ تم إضافة سؤال جديد للمادة: {subject}")
            return success
            
        except Exception as e:
            logger.error(f"❌ خطأ في إضافة السؤال: {e}")
            return False
    
    def update_question(self, subject: str, question_id: str, question_data: Dict[str, Any]) -> bool:
        """تحديث سؤال موجود"""
        try:
            if subject not in self.questions_data.get("subjects", {}):
                return False
            
            questions = self.questions_data["subjects"][subject]["questions"]
            for i, question in enumerate(questions):
                if question.get("id") == question_id:
                    questions[i] = question_data
                    
                    # حفظ في Firebase
                    success = firebase_manager.save_mcq_questions(self.questions_data)
                    if success:
                        logger.info(f"✅ تم تحديث السؤال: {question_id}")
                    return success
            
            logger.warning(f"⚠️ السؤال غير موجود: {question_id}")
            return False
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحديث السؤال: {e}")
            return False
    
    def delete_question(self, subject: str, question_id: str) -> bool:
        """حذف سؤال"""
        try:
            if subject not in self.questions_data.get("subjects", {}):
                return False
            
            questions = self.questions_data["subjects"][subject]["questions"]
            for i, question in enumerate(questions):
                if question.get("id") == question_id:
                    del questions[i]
                    
                    # حفظ في Firebase
                    success = firebase_manager.save_mcq_questions(self.questions_data)
                    if success:
                        logger.info(f"✅ تم حذف السؤال: {question_id}")
                    return success
            
            logger.warning(f"⚠️ السؤال غير موجود: {question_id}")
            return False
            
        except Exception as e:
            logger.error(f"❌ خطأ في حذف السؤال: {e}")
            return False
    
    def get_default_settings(self) -> Dict[str, Any]:
        """الإعدادات الافتراضية للمادة"""
        return {
            "questions_per_test": 10,
            "time_limit": 15,
            "shuffle_questions": True,
            "shuffle_answers": True,
            "show_correct_answer": True,
            "show_explanation": True,
            "passing_score": 60
        }
    
    def update_subject_settings(self, subject: str, settings: Dict[str, Any]) -> bool:
        """تحديث إعدادات المادة"""
        try:
            if subject not in self.questions_data.get("subjects", {}):
                return False
            
            self.questions_data["subjects"][subject]["settings"].update(settings)
            
            # حفظ في Firebase
            success = firebase_manager.save_mcq_questions(self.questions_data)
            if success:
                logger.info(f"✅ تم تحديث إعدادات المادة: {subject}")
            return success
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحديث إعدادات المادة: {e}")
            return False
    
    def reload_questions(self):
        """إعادة تحميل الأسئلة من Firebase"""
        self.load_questions_from_firebase()

# إنشاء مثيل مدير أسئلة MCQ
mcq_manager = MCQManager()
