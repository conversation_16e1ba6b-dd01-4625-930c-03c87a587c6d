#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مبسط للنظام الجديد
Simple Test for New System
"""

import os
import sys
from datetime import datetime

# إضافة المجلد الحالي للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """اختبار الاستيرادات الأساسية"""
    try:
        print("🔄 اختبار الاستيرادات...")
        
        # اختبار telebot
        import telebot
        print("✅ telebot")
        
        # اختبار الملفات الجديدة
        from dynamic_content_manager import DynamicContentManager
        print("✅ DynamicContentManager")
        
        from dynamic_button_system import DynamicButtonSystem  
        print("✅ DynamicButtonSystem")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاستيرادات: {e}")
        return False

def test_classes():
    """اختبار إنشاء الكلاسات"""
    try:
        print("\n🔄 اختبار إنشاء الكلاسات...")
        
        # إنشاء بوت وهمي
        class MockBot:
            def send_message(self, chat_id, text, **kwargs):
                return f"Message to {chat_id}: {text[:50]}..."
            
            def edit_message_text(self, text, chat_id, message_id, **kwargs):
                return f"Edit {message_id}: {text[:50]}..."
            
            def answer_callback_query(self, call_id, text):
                return f"Callback {call_id}: {text}"
        
        mock_bot = MockBot()
        
        # اختبار DynamicContentManager
        from dynamic_content_manager import DynamicContentManager
        content_manager = DynamicContentManager(mock_bot)
        print("✅ DynamicContentManager instance created")
        print(f"   📋 Sections: {list(content_manager.content_structure.keys())}")
        
        # اختبار DynamicButtonSystem
        from dynamic_button_system import DynamicButtonSystem
        button_system = DynamicButtonSystem(mock_bot)
        print("✅ DynamicButtonSystem instance created")
        print(f"   🎛️ Button types: {list(button_system.button_types.keys())}")
        print(f"   ⚙️ Action types: {list(button_system.action_types.keys())}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الكلاسات: {e}")
        return False

def test_structure():
    """اختبار هيكل البيانات"""
    try:
        print("\n🔄 اختبار هيكل البيانات...")
        
        from dynamic_content_manager import DynamicContentManager
        
        class MockBot:
            pass
        
        manager = DynamicContentManager(MockBot())
        
        # اختبار هيكل المحتوى
        expected_sections = ["educational_content", "ministerial_materials", "services"]
        actual_sections = list(manager.content_structure.keys())
        
        print(f"   📂 Expected sections: {expected_sections}")
        print(f"   📂 Actual sections: {actual_sections}")
        
        for section in expected_sections:
            if section in actual_sections:
                print(f"   ✅ {section}")
                section_info = manager.content_structure[section]
                print(f"      📝 Name: {section_info.get('name')}")
                print(f"      📋 Type: {section_info.get('type')}")
            else:
                print(f"   ❌ {section} missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار هيكل البيانات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 اختبار مبسط للنظام الديناميكي الجديد")
    print("=" * 50)
    
    tests = [
        ("الاستيرادات الأساسية", test_imports),
        ("إنشاء الكلاسات", test_classes),
        ("هيكل البيانات", test_structure),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}")
        print("-" * 30)
        
        try:
            result = test_func()
            results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
                
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {e}")
            results.append((test_name, False))
    
    # النتائج النهائية
    print("\n" + "=" * 50)
    print("📊 النتائج:")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅" if result else "❌"
        print(f"{status} {test_name}")
    
    print(f"\n📈 النتيجة: {passed}/{total}")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت!")
        print("\n📋 الخطوات التالية:")
        print("1. تشغيل البوت: python educational_bot_fixed.py")
        print("2. اختبار لوحة التحكم مع مشرف")
        print("3. اختبار إضافة محتوى جديد")
    else:
        print("⚠️ بعض الاختبارات فشلت")
    
    print(f"\n📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
