#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير إعدادات البوت المتقدم للبوت التعليمي
Advanced Bot Settings Manager for Educational Bot

يدير جميع إعدادات سلوك البوت والتفاعل مع المستخدمين
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
from firebase_manager import firebase_manager

logger = logging.getLogger(__name__)

class BotSettingsManager:
    """مدير إعدادات البوت المتقدم"""
    
    def __init__(self):
        self.settings = {}
        self.load_settings_from_firebase()
    
    def load_settings_from_firebase(self):
        """تحميل إعدادات البوت من Firebase"""
        try:
            self.settings = firebase_manager.get_bot_settings() or self.get_default_settings()
            logger.info("✅ تم تحميل إعدادات البوت من Firebase")
        except Exception as e:
            logger.error(f"❌ خطأ في تحميل إعدادات البوت: {e}")
            self.settings = self.get_default_settings()
    
    def get_default_settings(self) -> Dict[str, Any]:
        """الإعدادات الافتراضية للبوت"""
        return {
            "behavior": {
                "message_display_mode": "edit",  # edit, new, both
                "typing_delay": 1.0,  # ثواني
                "response_delay": 0.5,  # ثواني
                "auto_delete_messages": False,
                "auto_delete_delay": 30,  # ثواني
                "show_processing_messages": True,
                "use_callback_notifications": True,
                "enable_user_feedback": True,
                "max_message_length": 4096,
                "enable_message_threading": False
            },
            "notifications": {
                "success_notifications": True,
                "error_notifications": True,
                "warning_notifications": True,
                "info_notifications": False,
                "admin_notifications": True,
                "user_action_notifications": False,
                "system_notifications": True,
                "notification_duration": 3,  # ثواني
                "notification_sound": True,
                "custom_notification_texts": {
                    "success": "✅ تم بنجاح",
                    "error": "❌ حدث خطأ",
                    "warning": "⚠️ تحذير",
                    "info": "ℹ️ معلومة",
                    "processing": "⏳ جاري المعالجة..."
                }
            },
            "features": {
                "enable_translation": True,
                "enable_mcq_questions": True,
                "enable_file_upload": True,
                "enable_idea_suggestions": True,
                "enable_services": True,
                "enable_educational_content": True,
                "enable_ministerial_questions": True,
                "enable_user_progress_tracking": True,
                "enable_bookmarks": True,
                "enable_search": True,
                "enable_favorites": False,
                "enable_user_notes": False,
                "enable_certificates": False,
                "enable_achievements": False
            },
            "limits": {
                "max_file_size_mb": 50,
                "max_text_length": 2000,
                "max_questions_per_test": 20,
                "test_time_limit_minutes": 30,
                "max_daily_tests": 5,
                "max_translation_requests_per_day": 100,
                "max_idea_suggestions_per_day": 10,
                "max_service_requests_per_day": 5,
                "rate_limit_seconds": 1,
                "max_concurrent_users": 1000
            },
            "appearance": {
                "use_emojis": True,
                "enable_markdown": True,
                "button_style": "modern",  # classic, modern, minimal
                "color_scheme": "default",  # default, dark, light, custom
                "font_size": "medium",  # small, medium, large
                "show_user_avatars": False,
                "show_timestamps": True,
                "show_message_ids": False,
                "compact_mode": False,
                "rtl_support": True
            },
            "security": {
                "enable_spam_protection": True,
                "max_messages_per_minute": 10,
                "enable_flood_protection": True,
                "block_suspicious_users": False,
                "require_phone_verification": False,
                "enable_captcha": False,
                "log_user_actions": True,
                "enable_ip_blocking": False,
                "session_timeout_hours": 24,
                "max_login_attempts": 5
            },
            "performance": {
                "cache_enabled": True,
                "cache_duration_minutes": 30,
                "preload_content": True,
                "optimize_images": True,
                "compress_responses": True,
                "enable_cdn": False,
                "max_memory_usage_mb": 512,
                "cleanup_interval_hours": 6,
                "database_connection_pool": 10,
                "enable_lazy_loading": True
            },
            "backup": {
                "auto_backup_enabled": True,
                "backup_interval_hours": 24,
                "max_backup_files": 7,
                "backup_location": "firebase",
                "include_user_data": True,
                "include_media_files": False,
                "compress_backups": True,
                "encrypt_backups": False
            },
            "analytics": {
                "track_user_interactions": True,
                "track_feature_usage": True,
                "track_performance_metrics": True,
                "track_error_rates": True,
                "generate_daily_reports": False,
                "generate_weekly_reports": True,
                "generate_monthly_reports": True,
                "export_analytics": False,
                "anonymize_user_data": True
            },
            "maintenance": {
                "maintenance_mode": False,
                "maintenance_message": "البوت قيد الصيانة، سيعود قريباً",
                "allow_admin_access": True,
                "scheduled_maintenance": False,
                "maintenance_start_time": "",
                "maintenance_end_time": "",
                "notify_users_before_maintenance": True,
                "maintenance_notification_hours": 2
            },
            "metadata": {
                "version": "2.0",
                "last_updated": datetime.now().isoformat(),
                "updated_by": "system",
                "settings_schema_version": "1.0"
            }
        }
    
    def get_setting(self, category: str, key: str, default=None):
        """الحصول على إعداد محدد"""
        try:
            return self.settings.get(category, {}).get(key, default)
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على الإعداد {category}.{key}: {e}")
            return default
    
    def update_setting(self, category: str, key: str, value: Any) -> bool:
        """تحديث إعداد محدد"""
        try:
            if category not in self.settings:
                self.settings[category] = {}
            
            self.settings[category][key] = value
            self.settings["metadata"]["last_updated"] = datetime.now().isoformat()
            
            success = firebase_manager.save_bot_settings(self.settings)
            if success:
                logger.info(f"✅ تم تحديث الإعداد {category}.{key}")
            return success
        except Exception as e:
            logger.error(f"❌ خطأ في تحديث الإعداد {category}.{key}: {e}")
            return False
    
    def update_category_settings(self, category: str, settings_dict: Dict[str, Any]) -> bool:
        """تحديث إعدادات فئة كاملة"""
        try:
            if category not in self.settings:
                self.settings[category] = {}
            
            self.settings[category].update(settings_dict)
            self.settings["metadata"]["last_updated"] = datetime.now().isoformat()
            
            success = firebase_manager.save_bot_settings(self.settings)
            if success:
                logger.info(f"✅ تم تحديث إعدادات فئة {category}")
            return success
        except Exception as e:
            logger.error(f"❌ خطأ في تحديث إعدادات فئة {category}: {e}")
            return False
    
    def get_category_settings(self, category: str) -> Dict[str, Any]:
        """الحصول على إعدادات فئة محددة"""
        return self.settings.get(category, {})
    
    def get_all_settings(self) -> Dict[str, Any]:
        """الحصول على جميع الإعدادات"""
        return self.settings
    
    def reset_category_to_default(self, category: str) -> bool:
        """إعادة تعيين فئة إعدادات للافتراضية"""
        try:
            default_settings = self.get_default_settings()
            if category in default_settings:
                self.settings[category] = default_settings[category]
                self.settings["metadata"]["last_updated"] = datetime.now().isoformat()
                
                success = firebase_manager.save_bot_settings(self.settings)
                if success:
                    logger.info(f"✅ تم إعادة تعيين إعدادات فئة {category}")
                return success
            return False
        except Exception as e:
            logger.error(f"❌ خطأ في إعادة تعيين إعدادات فئة {category}: {e}")
            return False
    
    def reset_all_to_default(self) -> bool:
        """إعادة تعيين جميع الإعدادات للافتراضية"""
        try:
            self.settings = self.get_default_settings()
            success = firebase_manager.save_bot_settings(self.settings)
            if success:
                logger.info("✅ تم إعادة تعيين جميع الإعدادات")
            return success
        except Exception as e:
            logger.error(f"❌ خطأ في إعادة تعيين جميع الإعدادات: {e}")
            return False
    
    def export_settings(self) -> Dict[str, Any]:
        """تصدير الإعدادات"""
        try:
            export_data = {
                "export_date": datetime.now().isoformat(),
                "version": self.settings.get("metadata", {}).get("version", "1.0"),
                "settings": self.settings
            }
            return export_data
        except Exception as e:
            logger.error(f"❌ خطأ في تصدير الإعدادات: {e}")
            return {}
    
    def import_settings(self, settings_data: Dict[str, Any]) -> bool:
        """استيراد الإعدادات"""
        try:
            if "settings" in settings_data:
                imported_settings = settings_data["settings"]
                
                # التحقق من صحة البيانات
                if self._validate_settings(imported_settings):
                    self.settings = imported_settings
                    self.settings["metadata"]["last_updated"] = datetime.now().isoformat()
                    self.settings["metadata"]["updated_by"] = "import"
                    
                    success = firebase_manager.save_bot_settings(self.settings)
                    if success:
                        logger.info("✅ تم استيراد الإعدادات بنجاح")
                    return success
                else:
                    logger.error("❌ بيانات الإعدادات المستوردة غير صحيحة")
                    return False
            return False
        except Exception as e:
            logger.error(f"❌ خطأ في استيراد الإعدادات: {e}")
            return False
    
    def _validate_settings(self, settings: Dict[str, Any]) -> bool:
        """التحقق من صحة الإعدادات"""
        try:
            required_categories = ["behavior", "notifications", "features", "limits"]
            for category in required_categories:
                if category not in settings:
                    return False
            return True
        except Exception:
            return False
    
    def get_settings_summary(self) -> Dict[str, Any]:
        """الحصول على ملخص الإعدادات"""
        try:
            summary = {
                "total_categories": len(self.settings),
                "enabled_features": len([k for k, v in self.settings.get("features", {}).items() if v]),
                "disabled_features": len([k for k, v in self.settings.get("features", {}).items() if not v]),
                "last_updated": self.settings.get("metadata", {}).get("last_updated", "غير محدد"),
                "version": self.settings.get("metadata", {}).get("version", "غير محدد"),
                "maintenance_mode": self.settings.get("maintenance", {}).get("maintenance_mode", False),
                "categories": list(self.settings.keys())
            }
            return summary
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء ملخص الإعدادات: {e}")
            return {}
    
    def reload_settings(self):
        """إعادة تحميل الإعدادات من Firebase"""
        self.load_settings_from_firebase()

# إنشاء مثيل مدير إعدادات البوت
bot_settings_manager = BotSettingsManager()
