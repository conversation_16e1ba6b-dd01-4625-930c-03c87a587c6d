#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎓 بوت تيليجرام تعليمي متعدد الأقسام - نسخة محسنة
Educational Telegram Bot with Enhanced Admin Panel

المطور: كرار الحدراوي
التاريخ: 2025-07-12
الوصف: بوت تعليمي احترافي مع لوحة تحكم شاملة للمشرفين
"""

import telebot
from telebot import types
import os
import logging
from datetime import datetime
import time
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

# استيراد مدير Firebase والمدراء الجدد
from firebase_manager import firebase_manager
from text_manager import text_manager
from button_manager import button_manager
from mcq_manager import mcq_manager
from admin_panel import create_admin_panel
from text_editor import create_text_editor
from question_editor import create_question_editor
from dynamic_content_manager import create_dynamic_content_manager
from dynamic_button_system import create_dynamic_button_system

# إعداد نظام التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bot.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# إعدادات البوت من متغيرات البيئة
BOT_TOKEN = os.getenv('BOT_TOKEN', 'YOUR_BOT_TOKEN_HERE')

# الحصول على معرفات المشرفين من Firebase
try:
    ADMIN_IDS = firebase_manager.get_admin_ids()
    if not ADMIN_IDS:
        # إذا لم توجد معرفات في Firebase، استخدم القيم الافتراضية
        ADMIN_IDS = [5445116367]
        logger.warning("⚠️ لم يتم العثور على مشرفين في Firebase، استخدام القيم الافتراضية")

        # إضافة المشرف الافتراضي إلى Firebase
        try:
            default_admin_data = {
                'telegram_id': 5445116367,
                'name': 'المشرف الافتراضي',
                'username': 'default_admin',
                'active': True,
                'added_date': datetime.now().isoformat(),
                'permissions': ['all']
            }
            firebase_manager.add_admin(5445116367, default_admin_data)
            logger.info("✅ تم إضافة المشرف الافتراضي إلى Firebase")
        except Exception as add_error:
            logger.error(f"❌ خطأ في إضافة المشرف الافتراضي: {add_error}")

except Exception as e:
    logger.error(f"❌ خطأ في الحصول على المشرفين من Firebase: {e}")
    ADMIN_IDS = [5445116367]

# إنشاء كائن البوت
bot = telebot.TeleBot(BOT_TOKEN)

# إنشاء مثيل لوحة التحكم الإدارية
admin_panel = create_admin_panel(bot)

# إنشاء مثيل محرر النصوص
text_editor = create_text_editor(bot)

# إنشاء مثيل محرر الأسئلة
question_editor = create_question_editor(bot)

# إنشاء مثيل مدير المحتوى الديناميكي
dynamic_content_manager = create_dynamic_content_manager(bot)

# إنشاء مثيل نظام الأزرار الديناميكي
dynamic_button_system = create_dynamic_button_system(bot)

# قاموس لحفظ حالات المستخدمين (مؤقت في الذاكرة)
user_states = {}

# تحميل البيانات من Firebase
def load_data_from_firebase():
    """تحميل البيانات من Firebase"""
    global educational_content, ministerial_questions, services_data

    try:
        # تحميل المحتوى التعليمي
        educational_content = firebase_manager.get_educational_content()
        if not educational_content:
            educational_content = get_default_educational_content()
            firebase_manager.save_educational_content(educational_content)

        # تحميل الأسئلة الوزارية
        ministerial_questions = firebase_manager.get_ministerial_questions()
        if not ministerial_questions:
            ministerial_questions = get_default_ministerial_questions()
            firebase_manager.save_ministerial_questions(ministerial_questions)

        # تحميل بيانات الخدمات
        services_data = firebase_manager.get_services_data()
        if not services_data:
            services_data = get_default_services_data()
            firebase_manager.save_services_data(services_data)

        logger.info("✅ تم تحميل البيانات من Firebase بنجاح")

    except Exception as e:
        logger.error(f"❌ خطأ في تحميل البيانات من Firebase: {e}")
        # استخدام البيانات الافتراضية في حالة الخطأ
        educational_content = get_default_educational_content()
        ministerial_questions = get_default_ministerial_questions()
        services_data = get_default_services_data()

def get_default_educational_content():
    """الحصول على المحتوى التعليمي الافتراضي"""
    return {
        "المرحلة الأولى": {
            "الفيزياء الطبية": {
                "books": ["كتاب الفيزياء الطبية الأساسية", "مبادئ الإشعاع الطبي"],
                "resources": ["ملزمة الفيزياء النووية", "دليل الأمان الإشعاعي"],
                "status": "متوفر"
            },
            "التشريح": {
                "books": ["أطلس التشريح البشري", "علم التشريح التطبيقي"],
                "resources": ["ملزمة التشريح المصورة", "نماذج ثلاثية الأبعاد"],
                "status": "متوفر"
            }
        },
        "المرحلة الثانية": {
            "تقنيات التصوير الطبي": {
                "books": ["أساسيات التصوير الإشعاعي", "تقنيات الأشعة المقطعية"],
                "resources": ["ملزمة التصوير بالرنين المغناطيسي", "حالات سريرية"],
                "status": "متوفر"
            },
            "علم الأمراض": {
                "books": ["علم الأمراض العام", "الأمراض الإشعاعية"],
                "resources": ["أطلس الأمراض المصور", "دراسات حالة"],
                "status": "متوفر"
            }
        },
        "المرحلة الثالثة": {
            "التصوير التشخيصي المتقدم": {
                "books": ["التصوير الطبي المتقدم", "تفسير الصور الإشعاعية"],
                "resources": ["ملزمة التشخيص الإشعاعي", "حالات معقدة"],
                "status": "متوفر"
            },
            "الطب النووي": {
                "books": ["أساسيات الطب النووي", "تطبيقات النظائر المشعة"],
                "resources": ["ملزمة الفحوصات النووية", "بروتوكولات العمل"],
                "status": "متوفر"
            }
        },
        "المرحلة الرابعة": {
            "التدريب السريري": {
                "books": ["دليل التدريب السريري", "إجراءات المستشفى"],
                "resources": ["ملزمة الحالات السريرية", "تقارير التدريب"],
                "status": "متوفر"
            },
            "مشروع التخرج": {
                "books": ["منهجية البحث العلمي", "كتابة الأطروحات"],
                "resources": ["نماذج مشاريع التخرج", "دليل التوثيق"],
                "status": "متوفر"
            }
        }
    }

def get_default_ministerial_questions():
    """الحصول على الأسئلة الوزارية الافتراضية"""
    return {
        "الفيزياء الطبية": {
            "2023": {
                "questions": [
                    {
                        "question": "ما هو مبدأ عمل جهاز الأشعة السينية؟",
                        "options": ["انبعاث الإلكترونات", "التأين الإشعاعي", "الرنين المغناطيسي", "الموجات فوق الصوتية"],
                        "correct": 0,
                        "explanation": "جهاز الأشعة السينية يعمل على مبدأ انبعاث الإلكترونات من الكاثود وضربها للأنود لإنتاج الأشعة السينية"
                    },
                    {
                        "question": "ما هي وحدة قياس الجرعة الإشعاعية؟",
                        "options": ["جراي (Gy)", "سيفرت (Sv)", "بيكريل (Bq)", "كوري (Ci)"],
                        "correct": 0,
                        "explanation": "الجراي (Gy) هي وحدة قياس الجرعة الإشعاعية الممتصة"
                    }
                ],
                "pdf_link": "https://example.com/physics_2023.pdf"
            }
        }
    }

def get_default_services_data():
    """الحصول على بيانات الخدمات الافتراضية"""
    return {
        "التصميم": {
            "description": "خدمات التصميم الجرافيكي والإبداعي",
            "specialists": ["أحمد محمد - مصمم جرافيك", "فاطمة علي - مصممة UI/UX"],
            "contact": "@design_team"
        },
        "البرمجة": {
            "description": "تطوير المواقع والتطبيقات والأنظمة",
            "specialists": ["محمد أحمد - مطور ويب", "سارة حسن - مطورة تطبيقات"],
            "contact": "@programming_team"
        },
        "كتابة المقالات": {
            "description": "كتابة المحتوى والمقالات العلمية والأدبية",
            "specialists": ["نور الدين - كاتب محتوى", "ليلى محمود - محررة"],
            "contact": "@writing_team"
        },
        "إعداد البحوث العلمية": {
            "description": "إعداد وتنسيق البحوث والدراسات العلمية",
            "specialists": ["د. عمر سالم - باحث أكاديمي", "د. هدى كريم - محررة علمية"],
            "contact": "@research_team"
        }
    }

class UserStates:
    """فئة لتعريف حالات المستخدمين"""
    MAIN_MENU = "main_menu"
    TRANSLATION = "translation"
    EDUCATIONAL_CONTENT = "educational_content"
    MINISTERIAL_MATERIALS = "ministerial_materials"
    SERVICES = "services"
    SUGGEST_IDEA = "suggest_idea"
    WAITING_FOR_TEXT = "waiting_for_text"
    WAITING_FOR_FILE = "waiting_for_file"
    WAITING_FOR_IDEA_NAME = "waiting_for_idea_name"
    WAITING_FOR_IDEA_AUTHOR = "waiting_for_idea_author"
    WAITING_FOR_IDEA_DESCRIPTION = "waiting_for_idea_description"
    WAITING_FOR_IDEA_CATEGORY = "waiting_for_idea_category"
    # حالات إدارية جديدة
    ADMIN_BROADCAST = "admin_broadcast"
    ADMIN_ADD_ADMIN = "admin_add_admin"
    ADMIN_REMOVE_ADMIN = "admin_remove_admin"

def create_main_keyboard(user_id=None):
    """إنشاء الكيبورد الرئيسي باستخدام النظام الديناميكي"""
    try:
        return button_manager.create_keyboard("main_menu", user_id)
    except Exception as e:
        logger.error(f"❌ خطأ في إنشاء الكيبورد الرئيسي: {e}")
        # كيبورد احتياطي في حالة الخطأ
        keyboard = types.ReplyKeyboardMarkup(resize_keyboard=True, row_width=2)

        # الأزرار الرئيسية
        btn_translation = types.KeyboardButton("📝 الترجمة")
        btn_content = types.KeyboardButton("📖 المحتوى العلمي")
        btn_ministerial = types.KeyboardButton("🗂️ المواد الوزارية")
        btn_services = types.KeyboardButton("🛠️ الخدمات")
        btn_suggest = types.KeyboardButton("💡 اقتراح فكرة جديدة")

        keyboard.add(btn_translation, btn_content)
        keyboard.add(btn_ministerial, btn_services)
        keyboard.add(btn_suggest)

        # إضافة زر لوحة التحكم للمشرفين فقط
        if user_id and user_id in ADMIN_IDS:
            btn_admin = types.KeyboardButton("🔧 لوحة التحكم")
            keyboard.add(btn_admin)

        return keyboard

def create_back_keyboard():
    """إنشاء كيبورد العودة للقائمة الرئيسية"""
    try:
        return button_manager.create_keyboard("back_button")
    except Exception as e:
        logger.error(f"❌ خطأ في إنشاء كيبورد العودة: {e}")
        # كيبورد احتياطي
        keyboard = types.ReplyKeyboardMarkup(resize_keyboard=True)
        btn_back = types.KeyboardButton("🔙 العودة للقائمة الرئيسية")
        keyboard.add(btn_back)
        return keyboard

def get_user_state(user_id):
    """الحصول على حالة المستخدم"""
    return user_states.get(user_id, UserStates.MAIN_MENU)

def set_user_state(user_id, state):
    """تعيين حالة المستخدم"""
    user_states[user_id] = state

def get_user_data(user_id):
    """الحصول على بيانات المستخدم من Firebase"""
    try:
        user_data = firebase_manager.get_user(user_id)
        if user_data is None:
            user_data = {}
        # التأكد من وجود المفاتيح الأساسية
        if 'user_id' not in user_data:
            user_data['user_id'] = user_id
        return user_data
    except Exception as e:
        logger.error(f"خطأ في الحصول على بيانات المستخدم {user_id}: {e}")
        return {'user_id': user_id}

def log_user_action(user_id, username, action):
    """تسجيل إجراءات المستخدمين"""
    logger.info(f"User {user_id} (@{username}) performed action: {action}")

    # حفظ النشاط في Firebase
    try:
        firebase_manager.update_user_activity(user_id, action)
    except Exception as e:
        logger.error(f"خطأ في حفظ نشاط المستخدم: {e}")

def save_user_data(user_id, data):
    """حفظ بيانات المستخدم في Firebase"""
    try:
        firebase_manager.save_user(user_id, data)
    except Exception as e:
        logger.error(f"خطأ في حفظ بيانات المستخدم {user_id}: {e}")

# تحميل البيانات عند بدء التشغيل
load_data_from_firebase()

@bot.message_handler(commands=['start'])
def start_command(message):
    """معالج أمر البدء"""
    user_id = message.from_user.id
    username = message.from_user.username or "Unknown"

    # تحديث معلومات المشرف إذا كان مشرفاً
    if user_id in ADMIN_IDS:
        user_info = {
            'username': message.from_user.username,
            'first_name': message.from_user.first_name,
            'last_name': message.from_user.last_name,
            'language_code': message.from_user.language_code
        }
        try:
            firebase_manager.update_admin_info(user_id, user_info)
        except Exception as e:
            logger.error(f"خطأ في تحديث معلومات المشرف {user_id}: {e}")

    log_user_action(user_id, username, "Started bot")
    set_user_state(user_id, UserStates.MAIN_MENU)

    # استخدام النصوص الديناميكية
    try:
        welcome_text = text_manager.get_text("welcome", "title") + "\n\n"
        welcome_text += text_manager.get_text("welcome", "description") + "\n\n"

        # إضافة أوصاف الأقسام
        sections = text_manager.get_text("welcome", "sections")
        if isinstance(sections, dict):
            for section_key, section_desc in sections.items():
                welcome_text += section_desc + "\n"

        # إضافة رسالة خاصة للمشرفين
        if user_id in ADMIN_IDS:
            welcome_text += "\n" + text_manager.get_text("welcome", "admin_note") + "\n"

        welcome_text += "\n" + text_manager.get_text("welcome", "footer")

    except Exception as e:
        logger.error(f"❌ خطأ في تحميل نص الترحيب: {e}")
        # نص احتياطي
        welcome_text = """
🎓 **أهلاً وسهلاً بك في البوت التعليمي المتطور!**

مرحباً بك في منصتك التعليمية الشاملة التي تضم:

📝 **الترجمة** - ترجمة الملفات والنصوص من الإنجليزية للعربية
📖 **المحتوى العلمي** - مواد دراسية منظمة حسب المرحلة
🗂️ **المواد الوزارية** - أسئلة وزارية مع الحلول واختبارات تفاعلية
🛠️ **الخدمات** - خدمات متنوعة من فريق المنصة
💡 **اقتراح فكرة جديدة** - شاركنا أفكارك الإبداعية

اختر القسم الذي تريده من الأزرار أدناه ⬇️
"""

    bot.send_message(
        message.chat.id,
        welcome_text,
        parse_mode='Markdown',
        reply_markup=create_main_keyboard(user_id)
    )

@bot.message_handler(commands=['add_admin'])
def add_admin_command(message):
    """أمر لإضافة مشرف جديد - يمكن استخدامه من قبل المطور"""
    global ADMIN_IDS
    user_id = message.from_user.id

    # السماح للمطور أو المشرف الحالي بإضافة مشرفين جدد
    if user_id not in ADMIN_IDS and user_id != 5445116367:  # معرف المطور الحقيقي
        bot.send_message(
            message.chat.id,
            "❌ عذراً، هذا الأمر متاح للمشرفين فقط."
        )
        return

    try:
        # إضافة المستخدم الحالي كمشرف
        admin_data = {
            'telegram_id': user_id,
            'name': message.from_user.first_name or 'مشرف جديد',
            'username': message.from_user.username or 'unknown',
            'active': True,
            'added_date': datetime.now().isoformat(),
            'permissions': ['all'],
            'added_by': 'self_command'
        }

        firebase_manager.add_admin(user_id, admin_data)

        # تحديث قائمة المشرفين
        ADMIN_IDS = firebase_manager.get_admin_ids()

        bot.send_message(
            message.chat.id,
            f"✅ **تم إضافتك كمشرف بنجاح!**\n\n🆔 **معرفك:** `{user_id}`\n👤 **الاسم:** {admin_data['name']}\n📅 **تاريخ الإضافة:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n🔧 يمكنك الآن الوصول إلى لوحة التحكم!",
            parse_mode='Markdown'
        )

        logger.info(f"✅ تم إضافة مشرف جديد: {admin_data['name']} ({user_id})")

    except Exception as e:
        logger.error(f"❌ خطأ في إضافة المشرف: {e}")
        bot.send_message(
            message.chat.id,
            f"❌ **خطأ في إضافة المشرف:**\n\n`{str(e)}`",
            parse_mode='Markdown'
        )

@bot.message_handler(commands=['my_id'])
def my_id_command(message):
    """أمر لعرض معرف المستخدم"""
    user_id = message.from_user.id
    username = message.from_user.username or "غير محدد"
    first_name = message.from_user.first_name or "غير محدد"

    bot.send_message(
        message.chat.id,
        f"🆔 **معلوماتك:**\n\n👤 **الاسم:** {first_name}\n🔗 **اسم المستخدم:** @{username}\n🆔 **معرف التيليجرام:** `{user_id}`\n\n💡 **ملاحظة:** يمكن استخدام هذا المعرف لإضافتك كمشرف في البوت.",
        parse_mode='Markdown'
    )

@bot.message_handler(func=lambda message: message.text == "🔧 لوحة التحكم")
def admin_control_panel(message):
    """لوحة التحكم الرئيسية للمشرفين - النسخة المحدثة"""
    user_id = message.from_user.id
    username = message.from_user.username or "Unknown"

    log_user_action(user_id, username, "Accessed admin control panel")

    # التحقق من صلاحيات الإدارة
    if user_id not in ADMIN_IDS:
        bot.send_message(
            message.chat.id,
            "❌ عذراً، هذه الميزة متاحة للمشرفين فقط.",
            reply_markup=create_main_keyboard(user_id)
        )
        return

    # عرض لوحة التحكم الجديدة مع الوظائف الديناميكية
    keyboard = types.InlineKeyboardMarkup(row_width=2)

    # الأزرار الرئيسية
    btn_content = types.InlineKeyboardButton("📚 إدارة المحتوى", callback_data="admin_dynamic_content")
    btn_buttons = types.InlineKeyboardButton("🎛️ إدارة الأزرار", callback_data="admin_dynamic_buttons")
    btn_stats = types.InlineKeyboardButton("📊 الإحصائيات", callback_data="admin_stats")
    btn_users = types.InlineKeyboardButton("👥 إدارة المستخدمين", callback_data="admin_users")
    btn_settings = types.InlineKeyboardButton("⚙️ الإعدادات", callback_data="admin_settings")
    btn_backup = types.InlineKeyboardButton("💾 النسخ الاحتياطي", callback_data="admin_backup")

    keyboard.add(btn_content, btn_buttons)
    keyboard.add(btn_stats, btn_users)
    keyboard.add(btn_settings, btn_backup)

    admin_text = f"""
🔧 **لوحة التحكم الإدارية الجديدة**

مرحباً {message.from_user.first_name or 'المشرف'} 👋

🎛️ **النظام الجديد للإدارة الديناميكية**

📚 **إدارة المحتوى** - تعديل وإضافة المحتوى بشكل ديناميكي
🎛️ **إدارة الأزرار** - إنشاء وتخصيص الأزرار مع نظام + المتطور
📊 **الإحصائيات** - عرض إحصائيات شاملة للبوت
👥 **إدارة المستخدمين** - إدارة المستخدمين والصلاحيات
⚙️ **الإعدادات** - تخصيص إعدادات البوت
💾 **النسخ الاحتياطي** - إنشاء واستعادة النسخ الاحتياطية

🔐 **معرف المشرف:** `{user_id}`
📅 **التاريخ:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

    bot.send_message(
        message.chat.id,
        admin_text,
        parse_mode='Markdown',
        reply_markup=keyboard
    )

@bot.message_handler(func=lambda message: message.text == "🔙 العودة للقائمة الرئيسية")
def back_to_main(message):
    """العودة للقائمة الرئيسية"""
    user_id = message.from_user.id
    set_user_state(user_id, UserStates.MAIN_MENU)

    bot.send_message(
        message.chat.id,
        "🏠 تم العودة للقائمة الرئيسية",
        reply_markup=create_main_keyboard(user_id)
    )

@bot.message_handler(func=lambda message: message.text == "📝 الترجمة")
def translation_section(message):
    """قسم الترجمة"""
    user_id = message.from_user.id
    username = message.from_user.username or "Unknown"

    log_user_action(user_id, username, "Accessed translation section")
    set_user_state(user_id, UserStates.TRANSLATION)

    translation_text = """
📝 **قسم الترجمة**

مرحباً بك في قسم الترجمة المتخصص!

🔹 **الخدمات المتاحة:**
• ترجمة النصوص من الإنجليزية للعربية
• ترجمة المستندات والملفات
• ترجمة المصطلحات الطبية
• مراجعة الترجمات

📤 **لبدء الترجمة:**
أرسل النص أو الملف الذي تريد ترجمته

💡 **نصائح:**
• للحصول على أفضل النتائج، أرسل نصوص واضحة
• يمكنك إرسال ملفات PDF أو Word أو صور
• المصطلحات الطبية لها أولوية خاصة
"""

    bot.send_message(
        message.chat.id,
        translation_text,
        parse_mode='Markdown',
        reply_markup=create_back_keyboard()
    )

@bot.message_handler(func=lambda message: message.text == "📖 المحتوى العلمي")
def educational_content_section(message):
    """قسم المحتوى العلمي"""
    user_id = message.from_user.id
    username = message.from_user.username or "Unknown"

    log_user_action(user_id, username, "Accessed educational content section")
    set_user_state(user_id, UserStates.EDUCATIONAL_CONTENT)

    # التحقق من وجود محتوى في قاعدة البيانات
    try:
        # تحميل المحتوى من Firebase
        content_from_db = firebase_manager.get_educational_content()

        if not content_from_db or len(content_from_db) == 0:
            # لا يوجد محتوى في قاعدة البيانات
            empty_content_text = """
📖 **المحتوى العلمي**

🔍 **لا يوجد محتوى متاح حالياً**

عذراً، لم يتم إضافة أي محتوى تعليمي بعد من قبل الإدارة.

💡 **ملاحظة للإدارة:**
يمكن إضافة المحتوى التعليمي من خلال لوحة التحكم الإدارية.

🔙 استخدم الزر أدناه للعودة للقائمة الرئيسية.
"""

            keyboard = types.InlineKeyboardMarkup()
            btn_back = types.InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data="back_main")
            keyboard.add(btn_back)

            bot.send_message(
                message.chat.id,
                empty_content_text,
                parse_mode='Markdown',
                reply_markup=keyboard
            )
            return

        # إنشاء كيبورد للمراحل الدراسية من قاعدة البيانات
        keyboard = types.InlineKeyboardMarkup(row_width=1)

        # ترتيب المراحل حسب الترقيم الصحيح
        stages_list = []
        for stage_name in content_from_db.keys():
            # استخراج رقم المرحلة للترتيب
            if "الأولى" in stage_name:
                order = 1
            elif "الثانية" in stage_name:
                order = 2
            elif "الثالثة" in stage_name:
                order = 3
            elif "الرابعة" in stage_name:
                order = 4
            else:
                order = 999  # للمراحل غير المعروفة

            stages_list.append((order, stage_name))

        # ترتيب المراحل
        stages_list.sort(key=lambda x: x[0])

        # إضافة أزرار المراحل مرتبة
        for order, stage_name in stages_list:
            btn = types.InlineKeyboardButton(
                f"📚 {stage_name}",
                callback_data=f"stage_{stage_name}"
            )
            keyboard.add(btn)

        # زر العودة
        btn_back = types.InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data="back_main")
        keyboard.add(btn_back)

        content_text = """
📖 **المحتوى العلمي**

مرحباً بك في مكتبة المحتوى العلمي الشاملة!

🎓 **المراحل الدراسية المتاحة:**

اختر المرحلة الدراسية التي تريد الوصول إلى محتواها:
"""

        bot.send_message(
            message.chat.id,
            content_text,
            parse_mode='Markdown',
            reply_markup=keyboard
        )

    except Exception as e:
        logger.error(f"❌ خطأ في تحميل المحتوى التعليمي: {e}")

        # رسالة خطأ
        error_text = """
📖 **المحتوى العلمي**

❌ **حدث خطأ في تحميل المحتوى**

عذراً، حدث خطأ تقني في تحميل المحتوى التعليمي.
يرجى المحاولة مرة أخرى أو التواصل مع الإدارة.

🔙 استخدم الزر أدناه للعودة للقائمة الرئيسية.
"""

        keyboard = types.InlineKeyboardMarkup()
        btn_back = types.InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data="back_main")
        keyboard.add(btn_back)

        bot.send_message(
            message.chat.id,
            error_text,
            parse_mode='Markdown',
            reply_markup=keyboard
        )

@bot.message_handler(func=lambda message: message.text == "🗂️ المواد الوزارية")
def ministerial_materials_section(message):
    """قسم المواد الوزارية"""
    user_id = message.from_user.id
    username = message.from_user.username or "Unknown"

    log_user_action(user_id, username, "Accessed ministerial materials section")
    set_user_state(user_id, UserStates.MINISTERIAL_MATERIALS)

    # التحقق من وجود مواد وزارية في قاعدة البيانات
    try:
        # تحميل المواد الوزارية من Firebase
        materials_from_db = firebase_manager.get_ministerial_questions()

        if not materials_from_db or len(materials_from_db) == 0:
            # لا توجد مواد وزارية في قاعدة البيانات
            empty_materials_text = """
🗂️ **المواد الوزارية**

🔍 **لا توجد مواد وزارية متاحة حالياً**

عذراً، لم يتم إضافة أي مواد وزارية بعد من قبل الإدارة.

💡 **ملاحظة للإدارة:**
يمكن إضافة المواد الوزارية والأسئلة من خلال لوحة التحكم الإدارية.

🔙 استخدم الزر أدناه للعودة للقائمة الرئيسية.
"""

            keyboard = types.InlineKeyboardMarkup()
            btn_back = types.InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data="back_main")
            keyboard.add(btn_back)

            bot.send_message(
                message.chat.id,
                empty_materials_text,
                parse_mode='Markdown',
                reply_markup=keyboard
            )
            return

        # إنشاء كيبورد للمواد من قاعدة البيانات
        keyboard = types.InlineKeyboardMarkup(row_width=1)

        # إضافة أزرار المواد
        for subject in materials_from_db.keys():
            btn = types.InlineKeyboardButton(
                f"📋 {subject}",
                callback_data=f"subject_{subject}"
            )
            keyboard.add(btn)

        # زر العودة
        btn_back = types.InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data="back_main")
        keyboard.add(btn_back)

        materials_text = """
🗂️ **المواد الوزارية**

مرحباً بك في قسم المواد الوزارية!

📋 **ما نوفره لك:**
• أسئلة وزارية للسنوات السابقة
• اختبارات تفاعلية مع الحلول
• شرح مفصل للإجابات
• إحصائيات الأداء

🎯 **المواد المتاحة:**

اختر المادة التي تريد دراستها:
"""

        bot.send_message(
            message.chat.id,
            materials_text,
            parse_mode='Markdown',
            reply_markup=keyboard
        )

    except Exception as e:
        logger.error(f"❌ خطأ في تحميل المواد الوزارية: {e}")

        # رسالة خطأ
        error_text = """
🗂️ **المواد الوزارية**

❌ **حدث خطأ في تحميل المواد الوزارية**

عذراً، حدث خطأ تقني في تحميل المواد الوزارية.
يرجى المحاولة مرة أخرى أو التواصل مع الإدارة.

🔙 استخدم الزر أدناه للعودة للقائمة الرئيسية.
"""

        keyboard = types.InlineKeyboardMarkup()
        btn_back = types.InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data="back_main")
        keyboard.add(btn_back)

        bot.send_message(
            message.chat.id,
            error_text,
            parse_mode='Markdown',
            reply_markup=keyboard
        )

@bot.message_handler(func=lambda message: message.text == "🛠️ الخدمات")
def services_section(message):
    """قسم الخدمات"""
    user_id = message.from_user.id
    username = message.from_user.username or "Unknown"

    log_user_action(user_id, username, "Accessed services section")
    set_user_state(user_id, UserStates.SERVICES)

    # التحقق من وجود خدمات في قاعدة البيانات
    try:
        # تحميل الخدمات من Firebase
        services_from_db = firebase_manager.get_services_data()

        if not services_from_db or len(services_from_db) == 0:
            # لا توجد خدمات في قاعدة البيانات
            empty_services_text = """
🛠️ **الخدمات**

🔍 **لا توجد خدمات متاحة حالياً**

عذراً، لم يتم إضافة أي خدمات بعد من قبل الإدارة.

💡 **ملاحظة للإدارة:**
يمكن إضافة الخدمات والمختصين من خلال لوحة التحكم الإدارية.

🔙 استخدم الزر أدناه للعودة للقائمة الرئيسية.
"""

            keyboard = types.InlineKeyboardMarkup()
            btn_back = types.InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data="back_main")
            keyboard.add(btn_back)

            bot.send_message(
                message.chat.id,
                empty_services_text,
                parse_mode='Markdown',
                reply_markup=keyboard
            )
            return

        # إنشاء كيبورد للخدمات من قاعدة البيانات
        keyboard = types.InlineKeyboardMarkup(row_width=1)

        # إضافة أزرار الخدمات
        for service_name in services_from_db.keys():
            btn = types.InlineKeyboardButton(
                f"🔧 {service_name}",
                callback_data=f"service_{service_name}"
            )
            keyboard.add(btn)

        # زر العودة
        btn_back = types.InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data="back_main")
        keyboard.add(btn_back)

        services_text = """
🛠️ **الخدمات**

مرحباً بك في قسم الخدمات المتخصصة!

💼 **فريق من المختصين في خدمتك**

اختر الخدمة التي تحتاجها من القائمة أدناه:
"""

        bot.send_message(
            message.chat.id,
            services_text,
            parse_mode='Markdown',
            reply_markup=keyboard
        )

    except Exception as e:
        logger.error(f"❌ خطأ في تحميل الخدمات: {e}")

        # رسالة خطأ
        error_text = """
�️ **الخدمات**

❌ **حدث خطأ في تحميل الخدمات**

عذراً، حدث خطأ تقني في تحميل الخدمات.
يرجى المحاولة مرة أخرى أو التواصل مع الإدارة.

� استخدم الزر أدناه للعودة للقائمة الرئيسية.
"""

        keyboard = types.InlineKeyboardMarkup()
        btn_back = types.InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data="back_main")
        keyboard.add(btn_back)

        bot.send_message(
            message.chat.id,
            error_text,
            parse_mode='Markdown',
            reply_markup=keyboard
        )

# معالج الرسائل النصية للجلسات الديناميكية
@bot.message_handler(func=lambda message: True, content_types=['text'])
def handle_dynamic_sessions(message):
    """معالج الرسائل النصية للجلسات الديناميكية"""
    user_id = message.from_user.id
    text = message.text

    # التحقق من وجود جلسة تحرير نشطة للمستخدم
    active_session = None
    session_id = None

    # البحث في جلسات المحتوى
    for sid, session in dynamic_content_manager.admin_sessions.items():
        if session.get("user_id") == user_id:
            active_session = session
            session_id = sid
            break

    # البحث في جلسات الأزرار إذا لم توجد جلسة محتوى
    if not active_session:
        for sid, session in dynamic_button_system.button_sessions.items():
            if session.get("user_id") == user_id:
                active_session = session
                session_id = sid
                break

    if active_session:
        # معالجة الرسالة حسب نوع الجلسة
        if active_session.get("action") == "add_item":
            handle_content_session_message(message, session_id, active_session, text)
        elif active_session.get("action") == "add_button":
            handle_button_session_message(message, session_id, active_session, text)
        return

    # إذا لم توجد جلسة نشطة، معالجة الرسالة بالطريقة العادية
    handle_regular_message(message)

def handle_content_session_message(message, session_id, session, text):
    """معالجة رسالة في جلسة تحرير المحتوى"""
    try:
        if session["step"] == "name":
            # حفظ اسم العنصر
            session["data"]["name"] = text
            session["step"] = "description"

            # طلب الوصف
            keyboard = types.InlineKeyboardMarkup()
            btn_skip = types.InlineKeyboardButton("⏭️ تخطي", callback_data=f"admin_skip_description_{session_id}")
            btn_cancel = types.InlineKeyboardButton("❌ إلغاء", callback_data=f"admin_cancel_session_{session_id}")
            keyboard.add(btn_skip, btn_cancel)

            bot.send_message(
                message.chat.id,
                f"""
✅ **تم حفظ الاسم:** {text}

📝 **الخطوة 2: الوصف (اختياري)**

أرسل وصفاً للعنصر أو اضغط "تخطي":

💡 **مثال:** "مادة تتناول أساسيات الفيزياء الطبية والإشعاع"
""",
                parse_mode='Markdown',
                reply_markup=keyboard
            )

        elif session["step"] == "description":
            # حفظ الوصف وإنهاء الجلسة
            session["data"]["description"] = text
            finish_content_session(message, session_id, session)

    except Exception as e:
        logger.error(f"❌ خطأ في معالجة جلسة المحتوى: {e}")
        bot.send_message(message.chat.id, "❌ حدث خطأ، يرجى المحاولة مرة أخرى")

def handle_button_session_message(message, session_id, session, text):
    """معالجة رسالة في جلسة تحرير الأزرار"""
    try:
        if session["step"] == "text":
            # حفظ نص الزر
            session["data"]["text"] = text
            session["step"] = "action"

            # اختيار نوع الإجراء
            keyboard = types.InlineKeyboardMarkup(row_width=2)

            for action_key, action_name in dynamic_button_system.action_types.items():
                btn = types.InlineKeyboardButton(
                    action_name,
                    callback_data=f"admin_button_action_{session_id}_{action_key}"
                )
                keyboard.add(btn)

            btn_cancel = types.InlineKeyboardButton("❌ إلغاء", callback_data=f"admin_cancel_button_session_{session_id}")
            keyboard.add(btn_cancel)

            bot.send_message(
                message.chat.id,
                f"""
✅ **تم حفظ نص الزر:** {text}

🎯 **الخطوة 3: نوع الإجراء**

اختر ما يحدث عند الضغط على الزر:

🔗 **انتقال لقسم:** الانتقال لقسم آخر في البوت
📄 **عرض محتوى:** عرض نص أو محتوى معين
🌐 **رابط خارجي:** فتح رابط خارجي
⚙️ **إجراء مخصص:** إجراء برمجي مخصص
""",
                parse_mode='Markdown',
                reply_markup=keyboard
            )

    except Exception as e:
        logger.error(f"❌ خطأ في معالجة جلسة الأزرار: {e}")
        bot.send_message(message.chat.id, "❌ حدث خطأ، يرجى المحاولة مرة أخرى")

def finish_content_session(message, session_id, session):
    """إنهاء جلسة تحرير المحتوى"""
    try:
        # إضافة العنصر الجديد
        section_key = session["section_key"]
        position = session["position"]
        item_data = session["data"]

        # تحميل المحتوى الحالي
        current_content = dynamic_content_manager._get_section_content(section_key)

        # إضافة العنصر الجديد
        if isinstance(current_content, dict):
            current_content[item_data["name"]] = {
                "description": item_data.get("description", ""),
                "created_date": datetime.now().isoformat(),
                "active": True
            }
        else:
            # إذا كان المحتوى قائمة
            if not isinstance(current_content, list):
                current_content = []
            current_content.insert(position, item_data["name"])

        # حفظ المحتوى المحدث
        success = dynamic_content_manager._save_section_content(section_key, current_content)

        if success:
            bot.send_message(
                message.chat.id,
                f"✅ **تم إضافة العنصر بنجاح!**\n\n📌 **الاسم:** {item_data['name']}\n📝 **الوصف:** {item_data.get('description', 'بدون وصف')}",
                parse_mode='Markdown'
            )
        else:
            bot.send_message(
                message.chat.id,
                "❌ **فشل في حفظ العنصر**\n\nيرجى المحاولة مرة أخرى",
                parse_mode='Markdown'
            )

        # حذف الجلسة
        del dynamic_content_manager.admin_sessions[session_id]

    except Exception as e:
        logger.error(f"❌ خطأ في إنهاء جلسة المحتوى: {e}")
        bot.send_message(message.chat.id, "❌ حدث خطأ في حفظ العنصر")

def handle_regular_message(message):
    """معالجة الرسائل العادية (غير المرتبطة بجلسات)"""
    # هنا يمكن إضافة معالجة الرسائل العادية
    # مثل الترجمة أو الاقتراحات
    pass

# معالجات الـ Callback Queries
@bot.callback_query_handler(func=lambda _: True)
def handle_callback_query(call):
    """معالج الـ callback queries"""
    user_id = call.from_user.id
    data = call.data

    # إشعار احترافي للمستخدم
    bot.answer_callback_query(call.id, "تم اختيار هذا القسم ✅")

    try:
        if data == "back_main":
            # العودة للقائمة الرئيسية
            set_user_state(user_id, UserStates.MAIN_MENU)
            bot.edit_message_text(
                "🏠 تم العودة للقائمة الرئيسية",
                call.message.chat.id,
                call.message.message_id,
                reply_markup=None
            )
            bot.send_message(
                call.message.chat.id,
                "اختر القسم المطلوب:",
                reply_markup=create_main_keyboard(user_id)
            )

        elif data.startswith("admin_"):
            # معالجة الأوامر الإدارية باستخدام النظام الجديد
            if data == "admin_dynamic_content":
                # عرض لوحة إدارة المحتوى الديناميكي
                dynamic_content_manager.show_admin_content_panel(call.message.chat.id, call.message.message_id, user_id)
            elif data == "admin_dynamic_buttons":
                # عرض لوحة إدارة الأزرار الديناميكية
                dynamic_button_system.show_admin_button_panel(call.message.chat.id, call.message.message_id)
            elif data.startswith("edit_sec_"):
                # تعديل قسم معين
                section_key = data.replace("edit_sec_", "")
                dynamic_content_manager.show_section_edit_panel(call, section_key)
            elif data.startswith("add_item_"):
                # إضافة عنصر جديد
                parts = data.replace("add_item_", "").split("_")
                if len(parts) >= 2:
                    section_key = parts[0]
                    position = int(parts[1])
                    dynamic_content_manager.handle_add_item(call, section_key, position)
            elif data.startswith("add_btn_"):
                # إضافة زر جديد
                parts = data.replace("add_btn_", "").split("_")
                if len(parts) >= 2:
                    section_key = parts[0]
                    position = int(parts[1])
                    dynamic_button_system.handle_add_button(call, section_key, position)
            elif data.startswith("btn_type_"):
                # اختيار نوع الزر
                parts = data.replace("btn_type_", "").split("_")
                if len(parts) >= 2:
                    session_id = "_".join(parts[:-1])
                    button_type = parts[-1]
                    dynamic_button_system.handle_button_type_selection(call, session_id, button_type)
            elif data.startswith("btn_cancel_"):
                # إلغاء جلسة الزر
                session_id = data.replace("btn_cancel_", "")
                if session_id in dynamic_button_system.button_sessions:
                    del dynamic_button_system.button_sessions[session_id]
                bot.answer_callback_query(call.id, "❌ تم إلغاء العملية")
                # العودة للوحة التحكم
                dynamic_content_manager.show_admin_content_panel(call.message.chat.id, call.message.message_id, user_id)
            elif data.startswith("cancel_"):
                # إلغاء جلسة المحتوى
                session_id = data.replace("cancel_", "")
                if session_id in dynamic_content_manager.admin_sessions:
                    del dynamic_content_manager.admin_sessions[session_id]
                bot.answer_callback_query(call.id, "❌ تم إلغاء العملية")
                # العودة للوحة التحكم
                dynamic_content_manager.show_admin_content_panel(call.message.chat.id, call.message.message_id, user_id)
            else:
                # استخدام النظام القديم للأوامر الأخرى
                handle_admin_callback_new(call, data)

        elif data.startswith("category_"):
            # معالجة اختيار قسم الفكرة
            handle_idea_category(call, data)

        elif data.startswith("stage_"):
            # معالجة اختيار المرحلة الدراسية
            handle_educational_stage(call, data)

        elif data.startswith("subject_"):
            # معالجة اختيار المادة الوزارية
            handle_ministerial_subject(call, data)

        elif data.startswith("service_"):
            # معالجة اختيار الخدمة
            handle_service_selection(call, data)

        elif data.startswith("edu_subject_"):
            # معالجة اختيار مادة تعليمية
            handle_educational_subject(call, data)

        elif data.startswith("year_"):
            # معالجة اختيار سنة وزارية
            handle_ministerial_year(call, data)

        elif data.startswith("quick_test_"):
            # معالجة الاختبار السريع
            handle_quick_test(call, data)

        elif data.startswith("my_stats_"):
            # معالجة عرض الإحصائيات
            handle_my_stats(call, data)

        elif data.startswith("request_service_"):
            # معالجة طلب الخدمة
            handle_service_request(call, data)

        elif data == "back_educational":
            # العودة لقسم المحتوى العلمي
            educational_content_section_callback(call)

        elif data == "back_ministerial":
            # العودة لقسم المواد الوزارية
            ministerial_materials_section_callback(call)

        elif data == "back_services":
            # العودة لقسم الخدمات
            services_section_callback(call)

    except Exception as e:
        logger.error(f"خطأ في معالجة callback query: {e}")
        bot.answer_callback_query(call.id, "حدث خطأ، يرجى المحاولة مرة أخرى ❌")

def handle_admin_callback_new(call, data):
    """معالج callback الإدارية الجديد"""
    user_id = call.from_user.id

    # التحقق من صلاحيات الإدارة
    if not admin_panel.is_admin(user_id):
        bot.answer_callback_query(call.id, "❌ غير مصرح لك")
        return

    try:
        if data == "admin_back":
            # العودة للوحة التحكم الرئيسية
            admin_panel.show_main_panel(call.message.chat.id, call.message.message_id, user_id)

        elif data == "admin_stats":
            # عرض الإحصائيات (استخدام النظام القديم مؤقتاً)
            handle_admin_callback(call, data)

        elif data == "admin_content":
            # إدارة المحتوى
            admin_panel.handle_content_management(call)

        elif data.startswith("admin_content_"):
            # معالجة أقسام إدارة المحتوى
            if data == "admin_content_educational":
                admin_panel.handle_educational_content_management(call)
            elif data == "admin_content_services":
                admin_panel.handle_services_management(call)
            elif data == "admin_content_files":
                admin_panel.handle_files_management(call)
            elif data.startswith("admin_service_category_"):
                # إدارة فئة خدمات محددة
                category_name = data.replace("admin_service_category_", "")
                admin_panel.handle_service_category_management(call, category_name)
            elif data == "admin_service_specialists":
                # إدارة المختصين
                admin_panel.handle_specialists_management(call)

            elif data == "admin_content_ministerial":
                # سيتم إضافتها لاحقاً
                bot.answer_callback_query(call.id, "🚧 قيد التطوير")
            elif data == "admin_content_files":
                # سيتم إضافتها لاحقاً
                bot.answer_callback_query(call.id, "🚧 قيد التطوير")
            elif data == "admin_content_structure":
                # سيتم إضافتها لاحقاً
                bot.answer_callback_query(call.id, "🚧 قيد التطوير")
            elif data == "admin_content_preview":
                admin_panel.handle_preview_management(call)

        elif data.startswith("admin_edu_"):
            # معالجة إدارة المحتوى التعليمي
            if data.startswith("admin_edu_stage_"):
                # إدارة مرحلة دراسية محددة
                stage_name = data.replace("admin_edu_stage_", "")
                admin_panel.handle_educational_stage_management(call, stage_name)
            else:
                # معالجة باقي عمليات المحتوى التعليمي
                bot.answer_callback_query(call.id, "🚧 قيد التطوير")

        elif data.startswith("admin_text"):
            # إدارة النصوص
            if data == "admin_text":
                admin_panel.handle_text_management(call)
            else:
                # معالجة أقسام النصوص المحددة
                pass

        elif data.startswith("text_edit_"):
            # معالجة عمليات تحرير النصوص
            if data.startswith("text_edit_section_"):
                section_key = data.replace("text_edit_section_", "")
                text_editor.show_section_texts(call, section_key)
            elif data.startswith("text_edit_key_"):
                parts = data.replace("text_edit_key_", "").split("_", 1)
                if len(parts) == 2:
                    section_key, text_key = parts
                    text_editor.show_text_editor(call, section_key, text_key)
            elif data.startswith("text_edit_start_"):
                parts = data.replace("text_edit_start_", "").split("_", 1)
                if len(parts) == 2:
                    section_key, text_key = parts
                    text_editor.start_text_editing(call, section_key, text_key)
            elif data.startswith("text_edit_preview_single_"):
                parts = data.replace("text_edit_preview_single_", "").split("_", 1)
                if len(parts) == 2:
                    section_key, text_key = parts
                    text_editor.preview_text(call, section_key, text_key)
            elif data == "text_edit_export":
                text_editor.export_texts(call)
            elif data == "text_edit_reset":
                text_editor.reset_texts_to_default(call)
            elif data == "text_edit_reset_confirm":
                text_editor.confirm_reset_texts(call)
            else:
                bot.answer_callback_query(call.id, "🚧 قيد التطوير")

        elif data.startswith("admin_buttons"):
            # إدارة الأزرار
            if data == "admin_buttons":
                admin_panel.handle_button_management(call)
            else:
                # معالجة أنواع الأزرار المحددة
                pass

        elif data.startswith("button_"):
            # معالجة عمليات تحرير الأزرار
            if data == "button_templates":
                admin_panel.button_editor.show_button_templates(call)
            elif data.startswith("button_edit_config_"):
                config_name = data.replace("button_edit_config_", "")
                admin_panel.button_editor.show_button_config_editor(call, config_name)
            elif data.startswith("button_preview_"):
                config_name = data.replace("button_preview_", "")
                admin_panel.button_editor.preview_button_config(call, config_name)
            else:
                bot.answer_callback_query(call.id, "🚧 قيد التطوير")

        elif data.startswith("admin_mcq"):
            # إدارة أسئلة MCQ
            if data == "admin_mcq":
                admin_panel.handle_mcq_management(call)
            elif data.startswith("admin_mcq_subject_"):
                # إدارة مادة محددة
                subject_name = data.replace("admin_mcq_subject_", "")
                admin_panel.handle_mcq_subject_management(call, subject_name)
            elif data.startswith("admin_mcq_settings_"):
                # إعدادات مادة محددة
                subject_name = data.replace("admin_mcq_settings_", "")
                admin_panel.handle_mcq_settings(call, subject_name)
            elif data.startswith("admin_mcq_toggle_"):
                # تبديل إعدادات MCQ
                parts = data.split("_")
                if len(parts) >= 5:
                    setting_type = parts[3]
                    subject_name = "_".join(parts[4:])
                    handle_mcq_toggle_setting(call, subject_name, setting_type)
            else:
                # معالجة باقي عمليات MCQ
                bot.answer_callback_query(call.id, "🚧 قيد التطوير")

        elif data.startswith("admin_settings"):
            # إدارة الإعدادات
            if data == "admin_settings":
                admin_panel.handle_settings_management(call)
            elif data == "admin_settings_behavior":
                admin_panel.handle_behavior_settings(call)
            elif data == "admin_settings_features":
                admin_panel.handle_features_settings(call)
            elif data.startswith("admin_settings_toggle_"):
                # معالجة تبديل الإعدادات
                setting_key = data.replace("admin_settings_toggle_", "")
                if setting_key.startswith("feature_"):
                    feature_key = setting_key.replace("feature_", "")
                    from bot_settings_manager import bot_settings_manager
                    current_value = bot_settings_manager.get_setting("features", feature_key, True)
                    new_value = not current_value
                    success = bot_settings_manager.update_setting("features", feature_key, new_value)
                    if success:
                        bot.answer_callback_query(call.id, f"✅ تم {'تفعيل' if new_value else 'إلغاء'} الميزة")
                        admin_panel.handle_features_settings(call)
                    else:
                        bot.answer_callback_query(call.id, "❌ فشل في التحديث")
                else:
                    # معالجة إعدادات السلوك
                    if setting_key == "message_mode":
                        from bot_settings_manager import bot_settings_manager
                        current_mode = bot_settings_manager.get_setting("behavior", "message_display_mode", "edit")
                        new_mode = "new" if current_mode == "edit" else "edit"
                        success = bot_settings_manager.update_setting("behavior", "message_display_mode", new_mode)
                        if success:
                            bot.answer_callback_query(call.id, f"✅ تم تغيير طريقة العرض إلى {new_mode}")
                            admin_panel.handle_behavior_settings(call)
                        else:
                            bot.answer_callback_query(call.id, "❌ فشل في التحديث")
                    else:
                        bot.answer_callback_query(call.id, "🚧 قيد التطوير")
            else:
                # معالجة باقي أقسام الإعدادات
                bot.answer_callback_query(call.id, "🚧 قيد التطوير")

        else:
            # استخدام النظام القديم للأوامر غير المدعومة حالياً
            handle_admin_callback(call, data)

        bot.answer_callback_query(call.id, "تم اختيار هذا القسم ✅")

    except Exception as e:
        logger.error(f"❌ خطأ في معالجة callback الإدارية: {e}")
        bot.answer_callback_query(call.id, "❌ حدث خطأ")

def handle_mcq_toggle_setting(call, subject_name: str, setting_type: str):
    """معالجة تبديل إعدادات MCQ"""
    try:
        subject_info = mcq_manager.get_subject_info(subject_name)
        if not subject_info:
            bot.answer_callback_query(call.id, "❌ المادة غير موجودة")
            return

        settings = subject_info.get("settings", {})

        # تبديل الإعداد المطلوب
        if setting_type == "shuffle":
            if "q" in call.data:  # shuffle questions
                current_value = settings.get("shuffle_questions", True)
                settings["shuffle_questions"] = not current_value
                message = f"{'✅' if not current_value else '❌'} تم {'تفعيل' if not current_value else 'إلغاء'} خلط الأسئلة"
            elif "a" in call.data:  # shuffle answers
                current_value = settings.get("shuffle_answers", True)
                settings["shuffle_answers"] = not current_value
                message = f"{'✅' if not current_value else '❌'} تم {'تفعيل' if not current_value else 'إلغاء'} خلط الإجابات"
        elif setting_type == "correct":
            current_value = settings.get("show_correct_answer", True)
            settings["show_correct_answer"] = not current_value
            message = f"{'✅' if not current_value else '❌'} تم {'تفعيل' if not current_value else 'إلغاء'} إظهار الإجابة الصحيحة"
        elif setting_type == "explanation":
            current_value = settings.get("show_explanation", True)
            settings["show_explanation"] = not current_value
            message = f"{'✅' if not current_value else '❌'} تم {'تفعيل' if not current_value else 'إلغاء'} إظهار الشرح"
        else:
            bot.answer_callback_query(call.id, "❌ إعداد غير معروف")
            return

        # حفظ الإعدادات
        success = mcq_manager.update_subject_settings(subject_name, settings)

        if success:
            bot.answer_callback_query(call.id, message)
            # إعادة عرض صفحة الإعدادات
            admin_panel.handle_mcq_settings(call, subject_name)
        else:
            bot.answer_callback_query(call.id, "❌ فشل في حفظ الإعدادات")

    except Exception as e:
        logger.error(f"❌ خطأ في تبديل إعدادات MCQ: {e}")
        bot.answer_callback_query(call.id, "❌ حدث خطأ")

def show_admin_panel_callback(call):
    """عرض لوحة التحكم الإدارية للـ callback queries"""
    user_id = call.from_user.id
    username = call.from_user.username or "Unknown"

    # إعادة تحميل معرفات المشرفين من Firebase للتأكد من التحديث
    global ADMIN_IDS
    try:
        updated_admin_ids = firebase_manager.get_admin_ids()
        if updated_admin_ids:
            ADMIN_IDS = updated_admin_ids
            logger.info(f"🔄 تم تحديث قائمة المشرفين: {len(ADMIN_IDS)} مشرف")
    except Exception as e:
        logger.error(f"❌ خطأ في تحديث قائمة المشرفين: {e}")

    # التحقق من صلاحيات الإدارة
    if user_id not in ADMIN_IDS:
        # طباعة معرف المستخدم للمساعدة في التشخيص
        logger.info(f"🔍 محاولة وصول غير مصرح بها للوحة التحكم عبر callback - معرف المستخدم: {user_id}")
        logger.info(f"📋 قائمة المشرفين الحالية: {ADMIN_IDS}")

        bot.answer_callback_query(call.id, "❌ عذراً، هذه الميزة متاحة للمشرفين فقط.")

        # إرسال رسالة توضيحية
        bot.edit_message_text(
            f"❌ **عذراً، هذه الميزة متاحة للمشرفين فقط.**\n\n🔍 **معرفك:** `{user_id}`\n\n💡 إذا كنت مشرفاً، تأكد من إضافة معرفك إلى قاعدة البيانات.",
            call.message.chat.id,
            call.message.message_id,
            parse_mode='Markdown'
        )
        return

    log_user_action(user_id, username, "Accessed admin control panel via callback")

    keyboard = types.InlineKeyboardMarkup(row_width=2)

    # الأزرار الإدارية الرئيسية
    btn_stats = types.InlineKeyboardButton("📊 الإحصائيات", callback_data="admin_stats")
    btn_users = types.InlineKeyboardButton("👥 إدارة المستخدمين", callback_data="admin_users")
    btn_ideas = types.InlineKeyboardButton("💡 الأفكار المقترحة", callback_data="admin_ideas")
    btn_content = types.InlineKeyboardButton("📚 إدارة المحتوى", callback_data="admin_content")
    btn_admins = types.InlineKeyboardButton("👨‍💼 إدارة المشرفين", callback_data="admin_admins")
    btn_broadcast = types.InlineKeyboardButton("📢 رسالة جماعية", callback_data="admin_broadcast")
    btn_backup = types.InlineKeyboardButton("💾 النسخ الاحتياطي", callback_data="admin_backup")
    btn_settings = types.InlineKeyboardButton("⚙️ إعدادات البوت", callback_data="admin_settings")
    btn_logs = types.InlineKeyboardButton("📋 سجلات النشاط", callback_data="admin_logs")
    btn_maintenance = types.InlineKeyboardButton("🔧 صيانة النظام", callback_data="admin_maintenance")

    keyboard.add(btn_stats, btn_users)
    keyboard.add(btn_ideas, btn_content)
    keyboard.add(btn_admins, btn_broadcast)
    keyboard.add(btn_backup, btn_settings)
    keyboard.add(btn_logs, btn_maintenance)

    admin_text = f"""
🔧 **لوحة التحكم الإدارية**

مرحباً {call.from_user.first_name or 'المشرف'} 👋

🎛️ **لوحة التحكم الشاملة للبوت التعليمي**

📊 **الإحصائيات** - عرض إحصائيات شاملة للبوت
👥 **إدارة المستخدمين** - عرض وإدارة المستخدمين
💡 **الأفكار المقترحة** - مراجعة الأفكار الجديدة
📚 **إدارة المحتوى** - تحديث المحتوى التعليمي
👨‍💼 **إدارة المشرفين** - إضافة/إزالة المشرفين
📢 **رسالة جماعية** - إرسال رسائل لجميع المستخدمين
💾 **النسخ الاحتياطي** - إنشاء واستعادة النسخ الاحتياطية
⚙️ **إعدادات البوت** - تخصيص إعدادات البوت
📋 **سجلات النشاط** - عرض سجلات النشاط
🔧 **صيانة النظام** - أدوات الصيانة والتحديث

🔐 **معرف المشرف:** `{user_id}`
📅 **التاريخ:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

    bot.edit_message_text(
        admin_text,
        call.message.chat.id,
        call.message.message_id,
        parse_mode='Markdown',
        reply_markup=keyboard
    )

def handle_admin_callback(call, data):
    """معالجة الأوامر الإدارية"""
    user_id = call.from_user.id

    # إعادة تحميل معرفات المشرفين من Firebase للتأكد من التحديث
    global ADMIN_IDS
    try:
        updated_admin_ids = firebase_manager.get_admin_ids()
        if updated_admin_ids:
            ADMIN_IDS = updated_admin_ids
    except Exception as e:
        logger.error(f"❌ خطأ في تحديث قائمة المشرفين: {e}")

    # التحقق من صلاحيات الإدارة
    if user_id not in ADMIN_IDS:
        bot.answer_callback_query(call.id, "❌ غير مصرح لك")
        return

    if data == "admin_stats":
        # عرض الإحصائيات من Firebase
        try:
            stats = firebase_manager.get_bot_statistics()
            stats_text = f"""
📊 **إحصائيات البوت**

👥 **المستخدمين:**
• العدد الكلي: {stats.get('total_users', 0)}
• النشطين حالياً: {len(user_states)}

💡 **الأفكار المقترحة:**
• العدد الكلي: {stats.get('total_ideas', 0)}

🎯 **الاختبارات:**
• العدد الكلي: {stats.get('total_tests', 0)}

👨‍💼 **المشرفين:**
• النشطين: {stats.get('active_admins', 0)}

📚 **المحتوى:**
• المراحل الدراسية: {len(educational_content)}
• المواد: {sum(len(stage) for stage in educational_content.values())}
• الأسئلة الوزارية: {sum(len(subject) for subject in ministerial_questions.values())}

🛠️ **الخدمات:**
• عدد الخدمات: {len(services_data)}

📅 **التاريخ:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        except Exception as e:
            logger.error(f"خطأ في الحصول على الإحصائيات: {e}")
            stats_text = "❌ خطأ في تحميل الإحصائيات"

        # إضافة زر العودة
        keyboard = types.InlineKeyboardMarkup()
        btn_back = types.InlineKeyboardButton("🔙 العودة للوحة التحكم", callback_data="admin_back")
        keyboard.add(btn_back)

        bot.edit_message_text(
            stats_text,
            call.message.chat.id,
            call.message.message_id,
            parse_mode='Markdown',
            reply_markup=keyboard
        )

    elif data == "admin_ideas":
        # عرض الأفكار المقترحة
        try:
            suggested_ideas = firebase_manager.get_ideas(limit=5)
            if not suggested_ideas:
                ideas_text = "💡 لا توجد أفكار مقترحة حالياً"
            else:
                ideas_text = "💡 **الأفكار المقترحة:**\n\n"
                for idea in suggested_ideas:
                    # تحويل التاريخ إلى نص
                    created_date = idea.get('created_date', 'غير محدد')
                    if hasattr(created_date, 'strftime'):
                        created_date = created_date.strftime('%Y-%m-%d %H:%M')
                    ideas_text += f"""
🆔 **#{idea['id']}** - {idea.get('name', 'بدون اسم')}
👤 {idea.get('author', 'غير محدد')} | 📂 {idea.get('category', 'غير محدد')}
📅 {created_date}
📝 {idea.get('description', 'بدون وصف')[:100]}...
➖➖➖➖➖➖➖➖➖➖
"""
        except Exception as e:
            logger.error(f"خطأ في الحصول على الأفكار: {e}")
            ideas_text = "❌ خطأ في تحميل الأفكار المقترحة"

        # إضافة زر العودة
        keyboard = types.InlineKeyboardMarkup()
        btn_back = types.InlineKeyboardButton("🔙 العودة للوحة التحكم", callback_data="admin_back")
        keyboard.add(btn_back)

        bot.edit_message_text(
            ideas_text,
            call.message.chat.id,
            call.message.message_id,
            parse_mode='Markdown',
            reply_markup=keyboard
        )

    elif data == "admin_users":
        # عرض إدارة المستخدمين
        keyboard = types.InlineKeyboardMarkup(row_width=2)
        btn_active = types.InlineKeyboardButton("👥 المستخدمين النشطين", callback_data="admin_users_active")
        btn_all = types.InlineKeyboardButton("📊 جميع المستخدمين", callback_data="admin_users_all")
        btn_banned = types.InlineKeyboardButton("🚫 المحظورين", callback_data="admin_users_banned")
        btn_stats = types.InlineKeyboardButton("📈 إحصائيات المستخدمين", callback_data="admin_users_stats")
        btn_back = types.InlineKeyboardButton("🔙 العودة للوحة التحكم", callback_data="admin_back")

        keyboard.add(btn_active, btn_all)
        keyboard.add(btn_banned, btn_stats)
        keyboard.add(btn_back)

        users_text = """
👥 **إدارة المستخدمين**

اختر العملية المطلوبة:

👥 **المستخدمين النشطين** - عرض المستخدمين النشطين حالياً
📊 **جميع المستخدمين** - عرض قائمة بجميع المستخدمين
🚫 **المحظورين** - إدارة المستخدمين المحظورين
📈 **إحصائيات المستخدمين** - تفاصيل استخدام البوت
"""

        bot.edit_message_text(
            users_text,
            call.message.chat.id,
            call.message.message_id,
            parse_mode='Markdown',
            reply_markup=keyboard
        )

    elif data == "admin_back":
        # العودة للوحة التحكم الرئيسية
        show_admin_panel_callback(call)

    elif data == "admin_broadcast":
        # إرسال رسالة جماعية
        set_user_state(user_id, UserStates.ADMIN_BROADCAST)
        bot.edit_message_text(
            "📢 **إرسال رسالة جماعية**\n\nاكتب الرسالة التي تريد إرسالها لجميع المستخدمين:",
            call.message.chat.id,
            call.message.message_id,
            parse_mode='Markdown'
        )

    elif data == "admin_admins":
        # إدارة المشرفين والصلاحيات المتقدمة
        admin_panel.handle_permissions_management(call)

    elif data == "admin_backup":
        # النسخ الاحتياطي المتقدم
        admin_panel.handle_backup_management(call)

    elif data == "admin_backup_create":
        # إنشاء نسخة احتياطية
        try:
            bot.edit_message_text(
                "💾 جاري إنشاء النسخة الاحتياطية...",
                call.message.chat.id,
                call.message.message_id
            )

            success = firebase_manager.backup_data()

            if success:
                result_text = "✅ **تم إنشاء النسخة الاحتياطية بنجاح!**\n\n📅 التاريخ: " + datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            else:
                result_text = "❌ **فشل في إنشاء النسخة الاحتياطية**\n\nيرجى المحاولة مرة أخرى أو التحقق من السجلات."

        except Exception as e:
            logger.error(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            result_text = f"❌ **خطأ في إنشاء النسخة الاحتياطية:**\n\n`{str(e)}`"

        keyboard = types.InlineKeyboardMarkup()
        btn_back = types.InlineKeyboardButton("🔙 العودة للنسخ الاحتياطي", callback_data="admin_backup")
        keyboard.add(btn_back)

        bot.edit_message_text(
            result_text,
            call.message.chat.id,
            call.message.message_id,
            parse_mode='Markdown',
            reply_markup=keyboard
        )

    elif data.startswith("admin_backup_") and data != "admin_backup_create":
        # معالجة عمليات النسخ الاحتياطي المتقدمة
        if data == "admin_backup_create_full":
            admin_panel.handle_backup_create_full(call)
        elif data == "admin_backup_create_selective":
            admin_panel.handle_backup_create_selective(call)
        elif data == "admin_backup_full_with_files":
            # إنشاء نسخة احتياطية كاملة مع الملفات
            bot.answer_callback_query(call.id, "⏳ جاري إنشاء النسخة الاحتياطية...")
            from backup_manager import backup_manager
            result = backup_manager.create_full_backup(include_files=True, compress=True)
            if result["success"]:
                bot.answer_callback_query(call.id, f"✅ تم إنشاء النسخة الاحتياطية")
                admin_panel.handle_backup_management(call)
            else:
                bot.answer_callback_query(call.id, f"❌ فشل في إنشاء النسخة الاحتياطية")
        elif data == "admin_backup_full_without_files":
            # إنشاء نسخة احتياطية كاملة بدون الملفات
            bot.answer_callback_query(call.id, "⏳ جاري إنشاء النسخة الاحتياطية...")
            from backup_manager import backup_manager
            result = backup_manager.create_full_backup(include_files=False, compress=True)
            if result["success"]:
                bot.answer_callback_query(call.id, f"✅ تم إنشاء النسخة الاحتياطية")
                admin_panel.handle_backup_management(call)
            else:
                bot.answer_callback_query(call.id, f"❌ فشل في إنشاء النسخة الاحتياطية")
        else:
            # معالجة باقي عمليات النسخ الاحتياطي
            bot.answer_callback_query(call.id, "🚧 قيد التطوير")

    elif data.startswith("admin_preview_"):
        # معالجة عمليات المعاينة
        if data == "admin_preview_texts":
            bot.answer_callback_query(call.id, "🚧 معاينة النصوص قيد التطوير")
        elif data == "admin_preview_buttons":
            bot.answer_callback_query(call.id, "🚧 معاينة الأزرار قيد التطوير")
        elif data == "admin_preview_settings":
            bot.answer_callback_query(call.id, "🚧 معاينة الإعدادات قيد التطوير")
        elif data == "admin_preview_cleanup":
            # تنظيف جلسات المعاينة القديمة
            cleaned_count = admin_panel.preview_manager.cleanup_old_sessions(24)
            bot.answer_callback_query(call.id, f"✅ تم تنظيف {cleaned_count} جلسة قديمة")
            admin_panel.handle_preview_management(call)
        else:
            bot.answer_callback_query(call.id, "🚧 قيد التطوير")

    elif data.startswith("preview_"):
        # معالجة عمليات المعاينة المحددة
        if data.startswith("preview_apply_"):
            session_id = data.replace("preview_apply_text_", "").replace("preview_apply_button_", "").replace("preview_apply_settings_", "")
            success = admin_panel.preview_manager.apply_changes(call, session_id)
            if success:
                # العودة للقائمة المناسبة
                admin_panel.handle_preview_management(call)
        elif data.startswith("preview_test_"):
            session_id = data.replace("preview_test_text_", "").replace("preview_test_button_", "").replace("preview_test_settings_", "")
            admin_panel.preview_manager.test_component(call, session_id)
        elif data.startswith("preview_cancel_"):
            session_id = data.replace("preview_cancel_", "")
            admin_panel.preview_manager.cancel_preview(call, session_id)
            admin_panel.handle_preview_management(call)
        else:
            bot.answer_callback_query(call.id, "🚧 قيد التطوير")

    elif data.startswith("admin_permissions_"):
        # معالجة عمليات الصلاحيات
        if data == "admin_permissions_roles":
            bot.answer_callback_query(call.id, "🚧 إدارة الأدوار قيد التطوير")
        elif data == "admin_permissions_users":
            bot.answer_callback_query(call.id, "🚧 إدارة صلاحيات المستخدمين قيد التطوير")
        elif data == "admin_permissions_create_role":
            bot.answer_callback_query(call.id, "🚧 إنشاء دور جديد قيد التطوير")
        elif data == "admin_permissions_assign":
            bot.answer_callback_query(call.id, "🚧 تعيين صلاحيات قيد التطوير")
        elif data == "admin_permissions_view":
            bot.answer_callback_query(call.id, "🚧 عرض الصلاحيات قيد التطوير")
        elif data == "admin_permissions_audit":
            bot.answer_callback_query(call.id, "🚧 سجل الصلاحيات قيد التطوير")
        else:
            bot.answer_callback_query(call.id, "🚧 قيد التطوير")

def handle_idea_category(call, data):
    """معالجة اختيار قسم الفكرة"""
    user_id = call.from_user.id
    category = data.replace("category_", "")

    user_data_dict = get_user_data(user_id)
    idea_data = user_data_dict.get('idea', {})

    if category == "قسم جديد":
        # طلب اسم القسم الجديد
        set_user_state(user_id, UserStates.WAITING_FOR_IDEA_CATEGORY)
        bot.edit_message_text(
            "📂 اكتب اسم القسم الجديد:",
            call.message.chat.id,
            call.message.message_id
        )
        return

    idea_data['category'] = category

    # حفظ الفكرة في Firebase
    idea_record = {
        'name': idea_data.get('name', ''),
        'author': idea_data.get('author', ''),
        'description': idea_data.get('description', ''),
        'category': category,
        'user_id': user_id
    }

    idea_id = firebase_manager.save_idea(idea_record)

    # إشعار المشرفين
    for admin_id in ADMIN_IDS:
        try:
            admin_text = f"""
💡 **فكرة جديدة مقترحة**

📝 **اسم الفكرة:** {idea_data.get('name', 'غير محدد')}
👤 **صاحب الفكرة:** {idea_data.get('author', 'غير محدد')}
📂 **القسم:** {category}
📅 **التاريخ:** {datetime.now().strftime('%Y-%m-%d %H:%M')}

📋 **الوصف:**
{idea_data.get('description', 'غير محدد')}

🆔 **معرف المستخدم:** {user_id}
"""
            bot.send_message(admin_id, admin_text, parse_mode='Markdown')
        except:
            pass

    # رسالة شكر للمستخدم
    success_text = f"""
✅ **تم إرسال فكرتك بنجاح!**

📝 **ملخص الفكرة:**
• الاسم: {idea_data.get('name', 'غير محدد')}
• صاحب الفكرة: {idea_data.get('author', 'غير محدد')}
• القسم: {category}
• رقم الفكرة: #{idea_id if idea_id else 'غير محدد'}

🔔 **سيتم مراجعة فكرتك من قبل الفريق المختص**
📧 **وسيتم التواصل معك في حالة الموافقة**

شكراً لمساهمتك في تطوير المنصة! 🙏
"""

    bot.edit_message_text(
        success_text,
        call.message.chat.id,
        call.message.message_id,
        parse_mode='Markdown'
    )

    # إرسال القائمة الرئيسية
    bot.send_message(
        call.message.chat.id,
        "🏠 العودة للقائمة الرئيسية:",
        reply_markup=create_main_keyboard(user_id)
    )

    set_user_state(user_id, UserStates.MAIN_MENU)

    # تنظيف بيانات المستخدم
    if 'idea' in user_data_dict:
        del user_data_dict['idea']
        save_user_data(user_id, user_data_dict)

def handle_educational_stage(call, data):
    """معالجة اختيار المرحلة الدراسية"""
    user_id = call.from_user.id
    stage_name = data.replace("stage_", "")

    bot.answer_callback_query(call.id, f"تم اختيار {stage_name} ✅")

    if stage_name not in educational_content:
        bot.edit_message_text(
            "❌ المرحلة غير موجودة",
            call.message.chat.id,
            call.message.message_id
        )
        return

    stage_data = educational_content[stage_name]

    # إنشاء كيبورد للمواد في هذه المرحلة
    keyboard = types.InlineKeyboardMarkup(row_width=1)

    for subject_name, subject_info in stage_data.items():
        btn = types.InlineKeyboardButton(
            f"📚 {subject_name}",
            callback_data=f"edu_subject_{stage_name}_{subject_name}"
        )
        keyboard.add(btn)

    # زر العودة
    btn_back = types.InlineKeyboardButton("🔙 العودة للمراحل", callback_data="back_educational")
    keyboard.add(btn_back)

    stage_text = f"""
📖 **{stage_name}**

📚 **المواد المتاحة في هذه المرحلة:**

اختر المادة التي تريد دراستها:
"""

    bot.edit_message_text(
        stage_text,
        call.message.chat.id,
        call.message.message_id,
        parse_mode='Markdown',
        reply_markup=keyboard
    )

def handle_ministerial_subject(call, data):
    """معالجة اختيار المادة الوزارية"""
    user_id = call.from_user.id
    subject_name = data.replace("subject_", "")

    bot.answer_callback_query(call.id, f"تم اختيار {subject_name} ✅")

    if subject_name not in ministerial_questions:
        bot.edit_message_text(
            "❌ المادة غير موجودة",
            call.message.chat.id,
            call.message.message_id
        )
        return

    subject_data = ministerial_questions[subject_name]

    # إنشاء كيبورد للسنوات المتاحة
    keyboard = types.InlineKeyboardMarkup(row_width=2)

    for year in subject_data.keys():
        btn = types.InlineKeyboardButton(
            f"📅 {year}",
            callback_data=f"year_{subject_name}_{year}"
        )
        keyboard.add(btn)

    # أزرار إضافية
    btn_test = types.InlineKeyboardButton("🎯 اختبار سريع", callback_data=f"quick_test_{subject_name}")
    btn_stats = types.InlineKeyboardButton("📊 إحصائياتي", callback_data=f"my_stats_{subject_name}")
    keyboard.add(btn_test, btn_stats)

    # زر العودة
    btn_back = types.InlineKeyboardButton("🔙 العودة للمواد", callback_data="back_ministerial")
    keyboard.add(btn_back)

    subject_text = f"""
🗂️ **{subject_name}**

📋 **السنوات المتاحة:**

اختر السنة التي تريد دراسة أسئلتها:

🎯 **اختبار سريع** - اختبار عشوائي من جميع السنوات
📊 **إحصائياتي** - عرض أداءك في هذه المادة
"""

    bot.edit_message_text(
        subject_text,
        call.message.chat.id,
        call.message.message_id,
        parse_mode='Markdown',
        reply_markup=keyboard
    )

def handle_service_selection(call, data):
    """معالجة اختيار الخدمة"""
    user_id = call.from_user.id
    service_name = data.replace("service_", "")

    bot.answer_callback_query(call.id, f"تم اختيار {service_name} ✅")

    if service_name not in services_data:
        bot.edit_message_text(
            "❌ الخدمة غير موجودة",
            call.message.chat.id,
            call.message.message_id
        )
        return

    service_info = services_data[service_name]

    # إنشاء كيبورد للخدمة
    keyboard = types.InlineKeyboardMarkup(row_width=1)

    btn_contact = types.InlineKeyboardButton(
        "📞 التواصل مع الفريق",
        url=f"https://t.me/{service_info.get('contact', 'support').replace('@', '')}"
    )
    btn_request = types.InlineKeyboardButton(
        "📝 طلب الخدمة",
        callback_data=f"request_service_{service_name}"
    )

    keyboard.add(btn_contact, btn_request)

    # زر العودة
    btn_back = types.InlineKeyboardButton("🔙 العودة للخدمات", callback_data="back_services")
    keyboard.add(btn_back)

    # عرض المختصين
    specialists_text = ""
    if 'specialists' in service_info and service_info['specialists']:
        specialists_text = "\n👥 **المختصين:**\n"
        for specialist in service_info['specialists']:
            specialists_text += f"• {specialist}\n"

    service_text = f"""
🛠️ **{service_name}**

📝 **الوصف:**
{service_info.get('description', 'لا يوجد وصف متاح')}

{specialists_text}

📞 **للتواصل:** {service_info.get('contact', 'غير محدد')}

💡 **يمكنك:**
• التواصل المباشر مع الفريق
• طلب الخدمة عبر البوت
"""

    bot.edit_message_text(
        service_text,
        call.message.chat.id,
        call.message.message_id,
        parse_mode='Markdown',
        reply_markup=keyboard
    )

def handle_educational_subject(call, data):
    """معالجة اختيار مادة تعليمية"""
    parts = data.replace("edu_subject_", "").split("_", 1)
    if len(parts) != 2:
        bot.answer_callback_query(call.id, "❌ خطأ في البيانات")
        return

    stage_name, subject_name = parts
    bot.answer_callback_query(call.id, f"تم اختيار {subject_name} ✅")

    if stage_name not in educational_content or subject_name not in educational_content[stage_name]:
        bot.edit_message_text(
            "❌ المادة غير موجودة",
            call.message.chat.id,
            call.message.message_id
        )
        return

    subject_data = educational_content[stage_name][subject_name]

    # إنشاء كيبورد للمحتوى
    keyboard = types.InlineKeyboardMarkup(row_width=1)

    btn_books = types.InlineKeyboardButton("📚 الكتب", callback_data=f"books_{stage_name}_{subject_name}")
    btn_resources = types.InlineKeyboardButton("📋 المصادر", callback_data=f"resources_{stage_name}_{subject_name}")

    keyboard.add(btn_books, btn_resources)

    # زر العودة
    btn_back = types.InlineKeyboardButton(f"🔙 العودة لـ {stage_name}", callback_data=f"stage_{stage_name}")
    keyboard.add(btn_back)

    # عرض الكتب والمصادر
    books_text = ""
    if 'books' in subject_data and subject_data['books']:
        books_text = "\n📚 **الكتب المتاحة:**\n"
        for book in subject_data['books']:
            books_text += f"• {book}\n"

    resources_text = ""
    if 'resources' in subject_data and subject_data['resources']:
        resources_text = "\n📋 **المصادر الإضافية:**\n"
        for resource in subject_data['resources']:
            resources_text += f"• {resource}\n"

    status = subject_data.get('status', 'غير محدد')
    status_emoji = "✅" if status == "متوفر" else "⏳"

    subject_text = f"""
📖 **{subject_name}**
🎓 **المرحلة:** {stage_name}

{status_emoji} **الحالة:** {status}

{books_text}
{resources_text}

💡 **اختر نوع المحتوى الذي تريد الوصول إليه:**
"""

    bot.edit_message_text(
        subject_text,
        call.message.chat.id,
        call.message.message_id,
        parse_mode='Markdown',
        reply_markup=keyboard
    )

def handle_ministerial_year(call, data):
    """معالجة اختيار سنة وزارية"""
    parts = data.replace("year_", "").split("_", 1)
    if len(parts) != 2:
        bot.answer_callback_query(call.id, "❌ خطأ في البيانات")
        return

    subject_name, year = parts
    bot.answer_callback_query(call.id, f"تم اختيار سنة {year} ✅")

    if subject_name not in ministerial_questions or year not in ministerial_questions[subject_name]:
        bot.edit_message_text(
            "❌ السنة غير موجودة",
            call.message.chat.id,
            call.message.message_id
        )
        return

    year_data = ministerial_questions[subject_name][year]
    questions_count = len(year_data.get('questions', []))

    # إنشاء كيبورد للخيارات
    keyboard = types.InlineKeyboardMarkup(row_width=1)

    btn_start_test = types.InlineKeyboardButton(
        f"🎯 بدء الاختبار ({questions_count} سؤال)",
        callback_data=f"start_test_{subject_name}_{year}"
    )
    btn_view_questions = types.InlineKeyboardButton(
        "👁️ عرض الأسئلة",
        callback_data=f"view_questions_{subject_name}_{year}"
    )

    keyboard.add(btn_start_test, btn_view_questions)

    # إضافة رابط PDF إذا كان متوفراً
    if 'pdf_link' in year_data and year_data['pdf_link']:
        btn_pdf = types.InlineKeyboardButton("📄 تحميل PDF", url=year_data['pdf_link'])
        keyboard.add(btn_pdf)

    # زر العودة
    btn_back = types.InlineKeyboardButton(f"🔙 العودة لـ {subject_name}", callback_data=f"subject_{subject_name}")
    keyboard.add(btn_back)

    year_text = f"""
📅 **{subject_name} - سنة {year}**

📊 **إحصائيات:**
• عدد الأسئلة: {questions_count}
• نوع الاختبار: اختيار من متعدد

🎯 **الخيارات المتاحة:**
• **بدء الاختبار** - اختبار تفاعلي مع النتائج
• **عرض الأسئلة** - مراجعة الأسئلة والحلول

💡 **نصيحة:** ننصح بمراجعة الأسئلة أولاً قبل بدء الاختبار
"""

    bot.edit_message_text(
        year_text,
        call.message.chat.id,
        call.message.message_id,
        parse_mode='Markdown',
        reply_markup=keyboard
    )

def handle_quick_test(call, data):
    """معالجة الاختبار السريع"""
    subject_name = data.replace("quick_test_", "")
    bot.answer_callback_query(call.id, "🎯 جاري تحضير الاختبار السريع...")

    # جمع جميع الأسئلة من كل السنوات
    all_questions = []
    if subject_name in ministerial_questions:
        for year_data in ministerial_questions[subject_name].values():
            if 'questions' in year_data:
                all_questions.extend(year_data['questions'])

    if not all_questions:
        bot.edit_message_text(
            "❌ لا توجد أسئلة متاحة لهذه المادة",
            call.message.chat.id,
            call.message.message_id
        )
        return

    # اختيار 5 أسئلة عشوائية
    import random
    selected_questions = random.sample(all_questions, min(5, len(all_questions)))

    keyboard = types.InlineKeyboardMarkup(row_width=1)
    btn_start = types.InlineKeyboardButton(
        f"🚀 بدء الاختبار ({len(selected_questions)} أسئلة)",
        callback_data=f"start_quick_test_{subject_name}"
    )
    btn_back = types.InlineKeyboardButton(f"🔙 العودة لـ {subject_name}", callback_data=f"subject_{subject_name}")

    keyboard.add(btn_start, btn_back)

    test_text = f"""
🎯 **اختبار سريع - {subject_name}**

⚡ **اختبار سريع من جميع السنوات**

📊 **تفاصيل الاختبار:**
• عدد الأسئلة: {len(selected_questions)}
• مدة الاختبار: غير محدودة
• نوع الأسئلة: اختيار من متعدد
• المصدر: أسئلة وزارية متنوعة

🎯 **هل أنت مستعد لبدء الاختبار؟**
"""

    bot.edit_message_text(
        test_text,
        call.message.chat.id,
        call.message.message_id,
        parse_mode='Markdown',
        reply_markup=keyboard
    )

def handle_my_stats(call, data):
    """معالجة عرض الإحصائيات"""
    subject_name = data.replace("my_stats_", "")
    user_id = call.from_user.id

    bot.answer_callback_query(call.id, "📊 جاري تحميل إحصائياتك...")

    # الحصول على إحصائيات المستخدم من Firebase
    try:
        user_stats = firebase_manager.get_user_stats(user_id, subject_name)

        if not user_stats:
            stats_text = f"""
📊 **إحصائياتك في {subject_name}**

📈 **لم تقم بأي اختبارات بعد**

💡 **ابدأ أول اختبار لك لرؤية إحصائياتك!**
"""
        else:
            total_tests = user_stats.get('total_tests', 0)
            total_questions = user_stats.get('total_questions', 0)
            correct_answers = user_stats.get('correct_answers', 0)
            accuracy = (correct_answers / total_questions * 100) if total_questions > 0 else 0

            stats_text = f"""
📊 **إحصائياتك في {subject_name}**

🎯 **الأداء العام:**
• عدد الاختبارات: {total_tests}
• إجمالي الأسئلة: {total_questions}
• الإجابات الصحيحة: {correct_answers}
• نسبة النجاح: {accuracy:.1f}%

📈 **التقييم:**
{get_performance_rating(accuracy)}

📅 **آخر اختبار:** {user_stats.get('last_test_date', 'لم يتم بعد')}
"""
    except Exception as e:
        logger.error(f"خطأ في الحصول على إحصائيات المستخدم: {e}")
        stats_text = "❌ خطأ في تحميل الإحصائيات"

    keyboard = types.InlineKeyboardMarkup(row_width=1)
    btn_back = types.InlineKeyboardButton(f"🔙 العودة لـ {subject_name}", callback_data=f"subject_{subject_name}")
    keyboard.add(btn_back)

    bot.edit_message_text(
        stats_text,
        call.message.chat.id,
        call.message.message_id,
        parse_mode='Markdown',
        reply_markup=keyboard
    )

def get_performance_rating(accuracy):
    """الحصول على تقييم الأداء"""
    if accuracy >= 90:
        return "🌟 ممتاز جداً"
    elif accuracy >= 80:
        return "⭐ ممتاز"
    elif accuracy >= 70:
        return "👍 جيد جداً"
    elif accuracy >= 60:
        return "👌 جيد"
    elif accuracy >= 50:
        return "📈 مقبول"
    else:
        return "📚 يحتاج تحسين"

def handle_service_request(call, data):
    """معالجة طلب الخدمة"""
    service_name = data.replace("request_service_", "")
    user_id = call.from_user.id

    bot.answer_callback_query(call.id, "📝 جاري تحضير نموذج الطلب...")

    if service_name not in services_data:
        bot.edit_message_text(
            "❌ الخدمة غير موجودة",
            call.message.chat.id,
            call.message.message_id
        )
        return

    # حفظ حالة طلب الخدمة
    set_user_state(user_id, f"requesting_service_{service_name}")

    request_text = f"""
📝 **طلب خدمة: {service_name}**

لطلب هذه الخدمة، يرجى إرسال التفاصيل التالية:

📋 **المطلوب:**
• وصف مفصل للمشروع
• المتطلبات الخاصة
• الميزانية المتوقعة (إن وجدت)
• الموعد المطلوب للتسليم

💡 **مثال:**
"أحتاج تصميم شعار لشركة طبية، الألوان المفضلة: أزرق وأبيض، الميزانية: 50$، التسليم خلال أسبوع"

✍️ **اكتب طلبك الآن:**
"""

    keyboard = types.InlineKeyboardMarkup(row_width=1)
    btn_cancel = types.InlineKeyboardButton("❌ إلغاء الطلب", callback_data=f"service_{service_name}")
    keyboard.add(btn_cancel)

    bot.edit_message_text(
        request_text,
        call.message.chat.id,
        call.message.message_id,
        parse_mode='Markdown',
        reply_markup=keyboard
    )

def educational_content_section_callback(call):
    """عرض قسم المحتوى العلمي عبر callback"""
    # إنشاء كيبورد للمراحل الدراسية
    keyboard = types.InlineKeyboardMarkup(row_width=1)

    for stage in educational_content.keys():
        btn = types.InlineKeyboardButton(
            f"📚 {stage}",
            callback_data=f"stage_{stage}"
        )
        keyboard.add(btn)

    # زر العودة
    btn_back = types.InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data="back_main")
    keyboard.add(btn_back)

    content_text = """
📖 **المحتوى العلمي**

مرحباً بك في مكتبة المحتوى العلمي الشاملة!

🎓 **المراحل الدراسية المتاحة:**

اختر المرحلة الدراسية التي تريد الوصول إلى محتواها:
"""

    bot.edit_message_text(
        content_text,
        call.message.chat.id,
        call.message.message_id,
        parse_mode='Markdown',
        reply_markup=keyboard
    )

def ministerial_materials_section_callback(call):
    """عرض قسم المواد الوزارية عبر callback"""
    # إنشاء كيبورد للمواد
    keyboard = types.InlineKeyboardMarkup(row_width=1)

    for subject in ministerial_questions.keys():
        btn = types.InlineKeyboardButton(
            f"📋 {subject}",
            callback_data=f"subject_{subject}"
        )
        keyboard.add(btn)

    # زر العودة
    btn_back = types.InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data="back_main")
    keyboard.add(btn_back)

    materials_text = """
🗂️ **المواد الوزارية**

مرحباً بك في قسم المواد الوزارية!

📋 **ما نوفره لك:**
• أسئلة وزارية للسنوات السابقة
• اختبارات تفاعلية مع الحلول
• شرح مفصل للإجابات
• إحصائيات الأداء

🎯 **المواد المتاحة:**

اختر المادة التي تريد دراستها:
"""

    bot.edit_message_text(
        materials_text,
        call.message.chat.id,
        call.message.message_id,
        parse_mode='Markdown',
        reply_markup=keyboard
    )

def services_section_callback(call):
    """عرض قسم الخدمات عبر callback"""
    # إنشاء كيبورد للخدمات
    keyboard = types.InlineKeyboardMarkup(row_width=1)

    for service_name in services_data.keys():
        btn = types.InlineKeyboardButton(
            f"🔧 {service_name}",
            callback_data=f"service_{service_name}"
        )
        keyboard.add(btn)

    # زر العودة
    btn_back = types.InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data="back_main")
    keyboard.add(btn_back)

    services_text = """
🛠️ **الخدمات**

مرحباً بك في قسم الخدمات المتخصصة!

💼 **فريق من المختصين في خدمتك:**

🎨 **التصميم** - تصميم جرافيكي واحترافي
💻 **البرمجة** - تطوير المواقع والتطبيقات
✍️ **كتابة المقالات** - محتوى عالي الجودة
🔬 **إعداد البحوث العلمية** - بحوث أكاديمية متخصصة

اختر الخدمة التي تحتاجها:
"""

    bot.edit_message_text(
        services_text,
        call.message.chat.id,
        call.message.message_id,
        parse_mode='Markdown',
        reply_markup=keyboard
    )

@bot.message_handler(func=lambda message: message.text == "💡 اقتراح فكرة جديدة")
def suggest_idea_section(message):
    """قسم اقتراح الأفكار"""
    user_id = message.from_user.id
    username = message.from_user.username or "Unknown"

    log_user_action(user_id, username, "Accessed suggest idea section")
    set_user_state(user_id, UserStates.WAITING_FOR_IDEA_NAME)

    # تهيئة بيانات المستخدم
    user_data_dict = get_user_data(user_id)
    user_data_dict['idea'] = {}

    idea_text = """
💡 **اقتراح فكرة جديدة**

نحن نقدر أفكارك الإبداعية ونسعى لتطوير المنصة باستمرار!

🔹 **ما نحتاجه منك:**
• اسم الفكرة
• اسم صاحب الفكرة
• وصف مفصل للفكرة
• القسم المناسب لها

📝 **ابدأ بكتابة اسم الفكرة:**
"""

    bot.send_message(
        message.chat.id,
        idea_text,
        parse_mode='Markdown',
        reply_markup=create_back_keyboard()
    )

# معالجات الرسائل النصية
@bot.message_handler(content_types=['text'])
def handle_text_messages(message):
    """معالج الرسائل النصية"""
    user_id = message.from_user.id
    user_state = get_user_state(user_id)
    text = message.text

    if user_state == UserStates.WAITING_FOR_IDEA_NAME:
        # حفظ اسم الفكرة
        user_data_dict = get_user_data(user_id)
        if 'idea' not in user_data_dict:
            user_data_dict['idea'] = {}
        user_data_dict['idea']['name'] = text
        save_user_data(user_id, user_data_dict)
        set_user_state(user_id, UserStates.WAITING_FOR_IDEA_AUTHOR)

        bot.send_message(
            message.chat.id,
            "✅ تم حفظ اسم الفكرة\n\n👤 الآن اكتب اسم صاحب الفكرة:",
            reply_markup=create_back_keyboard()
        )

    elif user_state == UserStates.WAITING_FOR_IDEA_AUTHOR:
        # حفظ اسم صاحب الفكرة
        user_data_dict = get_user_data(user_id)
        if 'idea' not in user_data_dict:
            user_data_dict['idea'] = {}
        user_data_dict['idea']['author'] = text
        save_user_data(user_id, user_data_dict)
        set_user_state(user_id, UserStates.WAITING_FOR_IDEA_DESCRIPTION)

        bot.send_message(
            message.chat.id,
            "✅ تم حفظ اسم صاحب الفكرة\n\n📝 الآن اكتب وصف مفصل للفكرة:",
            reply_markup=create_back_keyboard()
        )

    elif user_state == UserStates.WAITING_FOR_IDEA_DESCRIPTION:
        # حفظ وصف الفكرة
        user_data_dict = get_user_data(user_id)
        if 'idea' not in user_data_dict:
            user_data_dict['idea'] = {}
        user_data_dict['idea']['description'] = text
        save_user_data(user_id, user_data_dict)
        set_user_state(user_id, UserStates.WAITING_FOR_IDEA_CATEGORY)

        # عرض الأقسام المتاحة
        keyboard = types.InlineKeyboardMarkup(row_width=1)
        categories = ["الترجمة", "المحتوى العلمي", "المواد الوزارية", "الخدمات", "قسم جديد"]

        for category in categories:
            btn = types.InlineKeyboardButton(category, callback_data=f"category_{category}")
            keyboard.add(btn)

        bot.send_message(
            message.chat.id,
            "✅ تم حفظ وصف الفكرة\n\n📂 اختر القسم المناسب للفكرة:",
            reply_markup=keyboard
        )

    elif user_state == UserStates.WAITING_FOR_IDEA_CATEGORY:
        # حفظ اسم القسم الجديد
        user_data_dict = get_user_data(user_id)
        if 'idea' not in user_data_dict:
            user_data_dict['idea'] = {}

        category = text.strip()
        idea_data = user_data_dict['idea']

        # حفظ الفكرة في Firebase
        idea_record = {
            'name': idea_data.get('name', ''),
            'author': idea_data.get('author', ''),
            'description': idea_data.get('description', ''),
            'category': category,
            'user_id': user_id
        }

        idea_id = firebase_manager.save_idea(idea_record)

        # إشعار المشرفين
        for admin_id in ADMIN_IDS:
            try:
                admin_text = f"""
💡 **فكرة جديدة مقترحة**

📝 **اسم الفكرة:** {idea_data.get('name', 'غير محدد')}
👤 **صاحب الفكرة:** {idea_data.get('author', 'غير محدد')}
📂 **القسم:** {category} (قسم جديد)
📅 **التاريخ:** {datetime.now().strftime('%Y-%m-%d %H:%M')}

📋 **الوصف:**
{idea_data.get('description', 'غير محدد')}

🆔 **معرف المستخدم:** {user_id}
"""
                bot.send_message(admin_id, admin_text, parse_mode='Markdown')
            except:
                pass

        # رسالة شكر للمستخدم
        success_text = f"""
✅ **تم إرسال فكرتك بنجاح!**

📝 **ملخص الفكرة:**
• الاسم: {idea_data.get('name', 'غير محدد')}
• صاحب الفكرة: {idea_data.get('author', 'غير محدد')}
• القسم: {category} (قسم جديد)
• رقم الفكرة: #{idea_id if idea_id else 'غير محدد'}

🔔 **سيتم مراجعة فكرتك من قبل الفريق المختص**
📧 **وسيتم التواصل معك في حالة الموافقة**

شكراً لمساهمتك في تطوير المنصة! 🙏
"""

        bot.send_message(
            message.chat.id,
            success_text,
            parse_mode='Markdown',
            reply_markup=create_main_keyboard(user_id)
        )

        set_user_state(user_id, UserStates.MAIN_MENU)

        # تنظيف بيانات المستخدم
        if 'idea' in user_data_dict:
            del user_data_dict['idea']
            save_user_data(user_id, user_data_dict)

    elif user_state == UserStates.ADMIN_BROADCAST:
        # إرسال رسالة جماعية (للمشرفين فقط)
        if user_id in ADMIN_IDS:
            try:
                # الحصول على قائمة جميع المستخدمين
                all_users = firebase_manager.get_all_users()
                sent_count = 0
                failed_count = 0

                broadcast_text = f"""
📢 **رسالة من إدارة البوت**

{text}

---
📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

                for user in all_users:
                    try:
                        bot.send_message(user['user_id'], broadcast_text, parse_mode='Markdown')
                        sent_count += 1
                        time.sleep(0.1)  # تجنب حدود التيليجرام
                    except:
                        failed_count += 1

                result_text = f"""
✅ **تم إرسال الرسالة الجماعية!**

📊 **الإحصائيات:**
• تم الإرسال بنجاح: {sent_count}
• فشل الإرسال: {failed_count}
• المجموع: {sent_count + failed_count}

📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

                bot.send_message(
                    message.chat.id,
                    result_text,
                    parse_mode='Markdown',
                    reply_markup=create_main_keyboard(user_id)
                )

                set_user_state(user_id, UserStates.MAIN_MENU)

            except Exception as e:
                logger.error(f"خطأ في إرسال الرسالة الجماعية: {e}")
                bot.send_message(
                    message.chat.id,
                    f"❌ خطأ في إرسال الرسالة الجماعية:\n\n`{str(e)}`",
                    parse_mode='Markdown'
                )
        else:
            bot.send_message(message.chat.id, "❌ غير مصرح لك بهذه العملية")

    elif user_state.startswith("requesting_service_"):
        # معالجة طلب الخدمة
        service_name = user_state.replace("requesting_service_", "")

        # حفظ طلب الخدمة
        service_request = {
            'user_id': user_id,
            'service_name': service_name,
            'request_details': text,
            'user_info': {
                'first_name': message.from_user.first_name,
                'username': message.from_user.username,
                'user_id': user_id
            },
            'status': 'pending',
            'created_date': datetime.now().isoformat()
        }

        try:
            request_id = firebase_manager.save_service_request(service_request)

            # إشعار المشرفين
            for admin_id in ADMIN_IDS:
                try:
                    admin_text = f"""
🔔 **طلب خدمة جديد**

🛠️ **الخدمة:** {service_name}
👤 **المستخدم:** {message.from_user.first_name or 'غير محدد'}
🆔 **معرف المستخدم:** {user_id}
📅 **التاريخ:** {datetime.now().strftime('%Y-%m-%d %H:%M')}

📝 **تفاصيل الطلب:**
{text}

🆔 **رقم الطلب:** #{request_id}
"""
                    bot.send_message(admin_id, admin_text, parse_mode='Markdown')
                except:
                    pass

            # رسالة تأكيد للمستخدم
            success_text = f"""
✅ **تم إرسال طلبك بنجاح!**

🛠️ **الخدمة:** {service_name}
🆔 **رقم الطلب:** #{request_id}
📅 **تاريخ الطلب:** {datetime.now().strftime('%Y-%m-%d %H:%M')}

📞 **سيتم التواصل معك قريباً من قبل الفريق المختص**

💡 **يمكنك حفظ رقم الطلب للمتابعة**

شكراً لثقتك بخدماتنا! 🙏
"""

            bot.send_message(
                message.chat.id,
                success_text,
                parse_mode='Markdown',
                reply_markup=create_main_keyboard(user_id)
            )

            set_user_state(user_id, UserStates.MAIN_MENU)

        except Exception as e:
            logger.error(f"خطأ في حفظ طلب الخدمة: {e}")
            bot.send_message(
                message.chat.id,
                "❌ حدث خطأ في إرسال الطلب. يرجى المحاولة مرة أخرى.",
                reply_markup=create_main_keyboard(user_id)
            )

    else:
        # التحقق من جلسات التحرير النشطة
        if text_editor.is_user_editing(user_id):
            # معالجة تحرير النصوص
            if text_editor.handle_text_input(message):
                return

        if question_editor.is_user_editing_question(user_id):
            # معالجة تحرير الأسئلة
            if question_editor.handle_question_input(message):
                return

        # رسالة غير معروفة
        if user_state == UserStates.MAIN_MENU:
            unknown_text = """
🤔 **لم أفهم طلبك**

يرجى استخدام الأزرار الموجودة أدناه للتنقل في البوت.

إذا كنت تواجه مشكلة، يمكنك استخدام الأمر /help للحصول على المساعدة.
"""
            bot.send_message(
                message.chat.id,
                unknown_text,
                parse_mode='Markdown',
                reply_markup=create_main_keyboard(user_id)
            )
        else:
            bot.send_message(
                message.chat.id,
                "🔄 يرجى إكمال العملية الحالية أو العودة للقائمة الرئيسية",
                reply_markup=create_back_keyboard()
            )

# معالج الأخطاء
def error_handler(func):
    """معالج الأخطاء العام"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logger.error(f"خطأ في {func.__name__}: {e}")
            if args and hasattr(args[0], 'chat'):
                try:
                    bot.send_message(
                        args[0].chat.id,
                        "❌ حدث خطأ مؤقت. يرجى المحاولة مرة أخرى."
                    )
                except:
                    pass
    return wrapper

# تطبيق معالج الأخطاء على الوظائف الرئيسية
start_command = error_handler(start_command)
handle_callback_query = error_handler(handle_callback_query)
handle_text_messages = error_handler(handle_text_messages)

if __name__ == "__main__":
    logger.info("🚀 بدء تشغيل البوت التعليمي المحسن...")
    logger.info(f"📊 تم تحميل {len(educational_content)} مراحل دراسية")
    logger.info(f"🗂️ تم تحميل {len(ministerial_questions)} مواد وزارية")
    logger.info(f"🛠️ تم تحميل {len(services_data)} خدمات")
    logger.info(f"👨‍💼 تم تحميل {len(ADMIN_IDS)} مشرف")

    try:
        # إرسال رسالة للمشرفين عند بدء التشغيل
        for admin_id in ADMIN_IDS:
            try:
                bot.send_message(
                    admin_id,
                    f"🚀 **تم بدء تشغيل البوت بنجاح!**\n\n📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n🔧 **لوحة التحكم متاحة الآن**",
                    parse_mode='Markdown'
                )
            except:
                pass

        logger.info("✅ البوت جاهز لاستقبال الرسائل")
        bot.polling(none_stop=True, interval=0, timeout=20)

    except KeyboardInterrupt:
        logger.info("🛑 تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل البوت: {e}")
        # إرسال إشعار للمشرفين عن الخطأ
        for admin_id in ADMIN_IDS:
            try:
                bot.send_message(
                    admin_id,
                    f"❌ **خطأ في البوت:**\n\n`{str(e)}`",
                    parse_mode='Markdown'
                )
            except:
                pass
