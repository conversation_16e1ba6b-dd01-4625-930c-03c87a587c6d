#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
محرر النصوص التفاعلي للبوت التعليمي
Interactive Text Editor for Educational Bot

يوفر واجهة تفاعلية لتحرير النصوص من لوحة التحكم
"""

import logging
from typing import Dict, Any, Optional, List
from telebot import types
from text_manager import text_manager

logger = logging.getLogger(__name__)

class TextEditor:
    """محرر النصوص التفاعلي"""
    
    def __init__(self, bot):
        self.bot = bot
        self.editing_sessions = {}  # جلسات التحرير النشطة
        self.text_templates = self.get_text_templates()
    
    def get_text_templates(self) -> Dict[str, Any]:
        """قوالب النصوص المتاحة"""
        return {
            "welcome": {
                "title": "نصوص الترحيب",
                "description": "النصوص التي تظهر عند بدء استخدام البوت",
                "keys": {
                    "title": "عنوان الترحيب الرئيسي",
                    "description": "وصف المنصة التعليمية",
                    "footer": "نص الختام والتوجيه",
                    "admin_note": "رسالة خاصة للمشرفين"
                },
                "variables": ["{user_name}", "{date}", "{time}"]
            },
            "translation": {
                "title": "نصوص الترجمة",
                "description": "النصوص المتعلقة بقسم الترجمة",
                "keys": {
                    "welcome": "رسالة الترحيب في قسم الترجمة",
                    "processing": "رسالة معالجة طلب الترجمة",
                    "preparing": "رسالة إعداد الملف للترجمة",
                    "completed": "رسالة اكتمال الترجمة",
                    "error": "رسالة خطأ في الترجمة"
                },
                "variables": ["{file_name}", "{file_size}", "{progress}", "{result}"]
            },
            "educational_content": {
                "title": "نصوص المحتوى العلمي",
                "description": "النصوص المتعلقة بالمحتوى التعليمي",
                "keys": {
                    "welcome": "رسالة الترحيب في المحتوى العلمي",
                    "stage_selected": "رسالة اختيار المرحلة",
                    "subject_selected": "رسالة اختيار المادة",
                    "no_content": "رسالة عدم توفر محتوى"
                },
                "variables": ["{stage_name}", "{subject_name}", "{content_list}", "{status}"]
            },
            "ministerial_materials": {
                "title": "نصوص المواد الوزارية",
                "description": "النصوص المتعلقة بالأسئلة الوزارية",
                "keys": {
                    "welcome": "رسالة الترحيب في المواد الوزارية",
                    "subject_selected": "رسالة اختيار المادة",
                    "test_start": "رسالة بدء الاختبار",
                    "question_format": "تنسيق عرض السؤال",
                    "test_completed": "رسالة اكتمال الاختبار"
                },
                "variables": ["{subject_name}", "{question_count}", "{time_limit}", "{current}", "{total}", "{score}", "{percentage}", "{grade}"]
            },
            "services": {
                "title": "نصوص الخدمات",
                "description": "النصوص المتعلقة بقسم الخدمات",
                "keys": {
                    "welcome": "رسالة الترحيب في الخدمات",
                    "service_selected": "رسالة اختيار الخدمة",
                    "contact_info": "معلومات التواصل"
                },
                "variables": ["{service_name}", "{description}", "{specialists}", "{contact}", "{contact_details}"]
            },
            "suggest_idea": {
                "title": "نصوص اقتراح الأفكار",
                "description": "النصوص المتعلقة باقتراح الأفكار",
                "keys": {
                    "welcome": "رسالة الترحيب في اقتراح الأفكار",
                    "name_saved": "رسالة حفظ اسم الفكرة",
                    "author_saved": "رسالة حفظ اسم صاحب الفكرة",
                    "description_saved": "رسالة حفظ وصف الفكرة",
                    "idea_submitted": "رسالة إرسال الفكرة بنجاح"
                },
                "variables": ["{name}", "{author}", "{category}", "{idea_id}"]
            },
            "admin": {
                "title": "نصوص الإدارة",
                "description": "النصوص المتعلقة بلوحة التحكم",
                "keys": {
                    "panel_title": "عنوان لوحة التحكم",
                    "welcome": "رسالة ترحيب المشرف",
                    "description": "وصف لوحة التحكم",
                    "unauthorized": "رسالة عدم التصريح",
                    "broadcast_prompt": "طلب كتابة الرسالة الجماعية",
                    "broadcast_success": "رسالة نجاح الإرسال الجماعي"
                },
                "variables": ["{admin_name}", "{user_id}", "{sent_count}", "{failed_count}", "{total_count}", "{date}"]
            },
            "common": {
                "title": "النصوص العامة",
                "description": "النصوص المشتركة في البوت",
                "keys": {
                    "back_to_main": "رسالة العودة للقائمة الرئيسية",
                    "choose_section": "رسالة اختيار القسم",
                    "unknown_message": "رسالة عدم فهم الطلب",
                    "complete_current": "رسالة إكمال العملية الحالية",
                    "error_occurred": "رسالة حدوث خطأ",
                    "loading": "رسالة التحميل",
                    "processing": "رسالة المعالجة",
                    "success": "رسالة النجاح",
                    "failed": "رسالة الفشل"
                },
                "variables": ["{user_name}", "{action}", "{error_details}"]
            }
        }
    
    def show_text_sections(self, call):
        """عرض أقسام النصوص"""
        try:
            keyboard = types.InlineKeyboardMarkup(row_width=2)
            
            for section_key, section_info in self.text_templates.items():
                btn = types.InlineKeyboardButton(
                    section_info["title"],
                    callback_data=f"text_edit_section_{section_key}"
                )
                keyboard.add(btn)
            
            # أزرار إضافية
            btn_preview = types.InlineKeyboardButton("👁️ معاينة جميع النصوص", callback_data="text_edit_preview_all")
            btn_export = types.InlineKeyboardButton("📤 تصدير النصوص", callback_data="text_edit_export")
            btn_import = types.InlineKeyboardButton("📥 استيراد النصوص", callback_data="text_edit_import")
            btn_reset = types.InlineKeyboardButton("🔄 إعادة تعيين افتراضية", callback_data="text_edit_reset")
            btn_back = types.InlineKeyboardButton("🔙 العودة للوحة التحكم", callback_data="admin_back")
            
            keyboard.add(btn_preview)
            keyboard.add(btn_export, btn_import)
            keyboard.add(btn_reset)
            keyboard.add(btn_back)
            
            text = """
🔤 **محرر النصوص التفاعلي**

اختر القسم الذي تريد تحرير نصوصه:

📝 **الأقسام المتاحة:**
• **نصوص الترحيب** - رسائل البداية والترحيب
• **نصوص الترجمة** - رسائل قسم الترجمة
• **نصوص المحتوى العلمي** - رسائل المحتوى التعليمي
• **نصوص المواد الوزارية** - رسائل الأسئلة والاختبارات
• **نصوص الخدمات** - رسائل قسم الخدمات
• **نصوص اقتراح الأفكار** - رسائل اقتراح الأفكار
• **نصوص الإدارة** - رسائل لوحة التحكم
• **النصوص العامة** - الرسائل المشتركة

🛠️ **أدوات إضافية:**
• **معاينة جميع النصوص** - عرض شامل لجميع النصوص
• **تصدير النصوص** - حفظ النصوص في ملف
• **استيراد النصوص** - تحميل النصوص من ملف
• **إعادة تعيين افتراضية** - استعادة النصوص الأصلية

💡 **ملاحظة:** يمكنك استخدام متغيرات ديناميكية في النصوص مثل {user_name} و {date}
"""
            
            self.bot.edit_message_text(
                text,
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown',
                reply_markup=keyboard
            )
            
        except Exception as e:
            logger.error(f"❌ خطأ في عرض أقسام النصوص: {e}")
            self.bot.answer_callback_query(call.id, "❌ حدث خطأ")
    
    def show_section_texts(self, call, section_key: str):
        """عرض نصوص قسم محدد"""
        try:
            if section_key not in self.text_templates:
                self.bot.answer_callback_query(call.id, "❌ قسم غير موجود")
                return
            
            section_info = self.text_templates[section_key]
            keyboard = types.InlineKeyboardMarkup(row_width=1)
            
            # عرض النصوص المتاحة في القسم
            for text_key, text_description in section_info["keys"].items():
                current_text = text_manager.get_text(section_key, text_key)
                # اختصار النص للعرض
                preview = current_text[:50] + "..." if len(current_text) > 50 else current_text
                
                btn = types.InlineKeyboardButton(
                    f"✏️ {text_description}",
                    callback_data=f"text_edit_key_{section_key}_{text_key}"
                )
                keyboard.add(btn)
            
            # أزرار إضافية
            btn_add_new = types.InlineKeyboardButton("➕ إضافة نص جديد", callback_data=f"text_edit_add_{section_key}")
            btn_preview_section = types.InlineKeyboardButton("👁️ معاينة القسم", callback_data=f"text_edit_preview_{section_key}")
            btn_back = types.InlineKeyboardButton("🔙 العودة لقائمة الأقسام", callback_data="admin_text")
            
            keyboard.add(btn_add_new)
            keyboard.add(btn_preview_section)
            keyboard.add(btn_back)
            
            # إنشاء قائمة المتغيرات المتاحة
            variables_text = ""
            if section_info.get("variables"):
                variables_text = "\n\n🔧 **المتغيرات المتاحة:**\n"
                for var in section_info["variables"]:
                    variables_text += f"• `{var}`\n"
            
            text = f"""
📝 **{section_info['title']}**

{section_info['description']}

اختر النص الذي تريد تحريره:
{variables_text}
💡 **تلميح:** انقر على أي نص لتحريره أو معاينته
"""
            
            self.bot.edit_message_text(
                text,
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown',
                reply_markup=keyboard
            )
            
        except Exception as e:
            logger.error(f"❌ خطأ في عرض نصوص القسم: {e}")
            self.bot.answer_callback_query(call.id, "❌ حدث خطأ")
    
    def show_text_editor(self, call, section_key: str, text_key: str):
        """عرض محرر النص"""
        try:
            if section_key not in self.text_templates:
                self.bot.answer_callback_query(call.id, "❌ قسم غير موجود")
                return
            
            section_info = self.text_templates[section_key]
            if text_key not in section_info["keys"]:
                self.bot.answer_callback_query(call.id, "❌ نص غير موجود")
                return
            
            current_text = text_manager.get_text(section_key, text_key)
            text_description = section_info["keys"][text_key]
            
            keyboard = types.InlineKeyboardMarkup(row_width=2)
            
            # أزرار التحرير
            btn_edit = types.InlineKeyboardButton("✏️ تحرير النص", callback_data=f"text_edit_start_{section_key}_{text_key}")
            btn_preview = types.InlineKeyboardButton("👁️ معاينة", callback_data=f"text_edit_preview_single_{section_key}_{text_key}")
            btn_reset = types.InlineKeyboardButton("🔄 إعادة تعيين", callback_data=f"text_edit_reset_single_{section_key}_{text_key}")
            btn_copy = types.InlineKeyboardButton("📋 نسخ النص", callback_data=f"text_edit_copy_{section_key}_{text_key}")
            btn_back = types.InlineKeyboardButton("🔙 العودة للقسم", callback_data=f"text_edit_section_{section_key}")
            
            keyboard.add(btn_edit, btn_preview)
            keyboard.add(btn_reset, btn_copy)
            keyboard.add(btn_back)
            
            # إنشاء قائمة المتغيرات المتاحة
            variables_text = ""
            if section_info.get("variables"):
                variables_text = "\n\n🔧 **المتغيرات المتاحة:**\n"
                for var in section_info["variables"]:
                    variables_text += f"• `{var}`\n"
            
            # عرض النص الحالي (مع تحديد الطول)
            display_text = current_text
            if len(display_text) > 500:
                display_text = display_text[:500] + "\n\n... (النص مقطوع للعرض)"
            
            text = f"""
✏️ **محرر النص**

📝 **القسم:** {section_info['title']}
🏷️ **النص:** {text_description}

📄 **النص الحالي:**
```
{display_text}
```

📊 **معلومات:**
• الطول: {len(current_text)} حرف
• عدد الأسطر: {current_text.count(chr(10)) + 1}
{variables_text}
🛠️ **اختر العملية المطلوبة:**
"""
            
            self.bot.edit_message_text(
                text,
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown',
                reply_markup=keyboard
            )
            
        except Exception as e:
            logger.error(f"❌ خطأ في عرض محرر النص: {e}")
            self.bot.answer_callback_query(call.id, "❌ حدث خطأ")
    
    def start_text_editing(self, call, section_key: str, text_key: str):
        """بدء تحرير النص"""
        try:
            user_id = call.from_user.id
            
            # إنشاء جلسة تحرير
            session_id = f"{user_id}_{section_key}_{text_key}"
            self.editing_sessions[session_id] = {
                "user_id": user_id,
                "section_key": section_key,
                "text_key": text_key,
                "chat_id": call.message.chat.id,
                "message_id": call.message.message_id,
                "original_text": text_manager.get_text(section_key, text_key),
                "start_time": datetime.now().isoformat()
            }
            
            current_text = text_manager.get_text(section_key, text_key)
            section_info = self.text_templates[section_key]
            text_description = section_info["keys"][text_key]
            
            keyboard = types.InlineKeyboardMarkup(row_width=2)
            btn_cancel = types.InlineKeyboardButton("❌ إلغاء", callback_data=f"text_edit_cancel_{session_id}")
            keyboard.add(btn_cancel)
            
            text = f"""
✏️ **تحرير النص**

📝 **القسم:** {section_info['title']}
🏷️ **النص:** {text_description}

📄 **النص الحالي:**
```
{current_text}
```

💬 **أرسل النص الجديد الآن:**

💡 **تلميحات:**
• يمكنك استخدام Markdown للتنسيق
• استخدم المتغيرات المتاحة للمحتوى الديناميكي
• أرسل "إلغاء" لإلغاء التحرير
"""
            
            self.bot.edit_message_text(
                text,
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown',
                reply_markup=keyboard
            )
            
            self.bot.answer_callback_query(call.id, "✏️ ابدأ بكتابة النص الجديد")
            
        except Exception as e:
            logger.error(f"❌ خطأ في بدء تحرير النص: {e}")
            self.bot.answer_callback_query(call.id, "❌ حدث خطأ")
    
    def handle_text_input(self, message):
        """معالجة إدخال النص الجديد"""
        try:
            user_id = message.from_user.id
            
            # البحث عن جلسة تحرير نشطة
            active_session = None
            for session_id, session in self.editing_sessions.items():
                if session["user_id"] == user_id:
                    active_session = session
                    break
            
            if not active_session:
                return False  # لا توجد جلسة تحرير نشطة
            
            new_text = message.text
            
            # التحقق من إلغاء التحرير
            if new_text.lower() in ["إلغاء", "cancel", "الغاء"]:
                self.cancel_editing(active_session)
                return True
            
            # حفظ النص الجديد
            success = text_manager.update_text(
                active_session["section_key"],
                active_session["text_key"],
                new_text
            )
            
            if success:
                # إرسال رسالة تأكيد
                keyboard = types.InlineKeyboardMarkup()
                btn_back = types.InlineKeyboardButton(
                    "🔙 العودة للمحرر",
                    callback_data=f"text_edit_key_{active_session['section_key']}_{active_session['text_key']}"
                )
                keyboard.add(btn_back)
                
                self.bot.send_message(
                    message.chat.id,
                    f"✅ **تم حفظ النص بنجاح!**\n\n📝 **القسم:** {active_session['section_key']}\n🏷️ **النص:** {active_session['text_key']}\n\n💾 تم حفظ التغييرات في قاعدة البيانات",
                    parse_mode='Markdown',
                    reply_markup=keyboard
                )
                
                # حذف الرسالة الأصلية
                try:
                    self.bot.delete_message(active_session["chat_id"], active_session["message_id"])
                except:
                    pass
                
            else:
                self.bot.send_message(
                    message.chat.id,
                    "❌ **فشل في حفظ النص**\n\nحدث خطأ أثناء حفظ النص. يرجى المحاولة مرة أخرى."
                )
            
            # إنهاء جلسة التحرير
            del self.editing_sessions[session_id]
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في معالجة إدخال النص: {e}")
            return False
    
    def cancel_editing(self, session):
        """إلغاء تحرير النص"""
        try:
            keyboard = types.InlineKeyboardMarkup()
            btn_back = types.InlineKeyboardButton(
                "🔙 العودة للمحرر",
                callback_data=f"text_edit_key_{session['section_key']}_{session['text_key']}"
            )
            keyboard.add(btn_back)
            
            self.bot.edit_message_text(
                "❌ **تم إلغاء تحرير النص**\n\nلم يتم حفظ أي تغييرات.",
                session["chat_id"],
                session["message_id"],
                parse_mode='Markdown',
                reply_markup=keyboard
            )
            
        except Exception as e:
            logger.error(f"❌ خطأ في إلغاء التحرير: {e}")

    def preview_text(self, call, section_key: str, text_key: str):
        """معاينة النص مع المتغيرات"""
        try:
            current_text = text_manager.get_text(section_key, text_key)
            section_info = self.text_templates[section_key]

            # تطبيق متغيرات تجريبية للمعاينة
            preview_vars = {
                "user_name": "أحمد محمد",
                "admin_name": "المشرف الرئيسي",
                "date": datetime.now().strftime("%Y-%m-%d"),
                "time": datetime.now().strftime("%H:%M"),
                "file_name": "مثال_ملف.pdf",
                "file_size": "2.5 MB",
                "service_name": "تصميم الشعارات",
                "stage_name": "المرحلة الأولى",
                "subject_name": "الفيزياء الطبية",
                "score": "85",
                "percentage": "85%",
                "grade": "جيد جداً"
            }

            try:
                preview_text = current_text.format(**preview_vars)
            except KeyError as e:
                preview_text = current_text + f"\n\n⚠️ متغير غير معرف: {e}"

            keyboard = types.InlineKeyboardMarkup(row_width=2)
            btn_edit = types.InlineKeyboardButton(
                "✏️ تحرير النص",
                callback_data=f"text_edit_start_{section_key}_{text_key}"
            )
            btn_copy = types.InlineKeyboardButton(
                "📋 نسخ النص",
                callback_data=f"text_edit_copy_{section_key}_{text_key}"
            )
            btn_back = types.InlineKeyboardButton(
                "🔙 العودة للمحرر",
                callback_data=f"text_edit_key_{section_key}_{text_key}"
            )

            keyboard.add(btn_edit, btn_copy)
            keyboard.add(btn_back)

            text = f"""
👁️ **معاينة النص**

📝 **القسم:** {section_info['title']}
🏷️ **النص:** {section_info['keys'][text_key]}

📄 **النص الأصلي:**
```
{current_text}
```

🎭 **المعاينة مع المتغيرات:**
{preview_text}

📊 **معلومات:**
• الطول: {len(current_text)} حرف
• عدد الأسطر: {current_text.count(chr(10)) + 1}
• المتغيرات المستخدمة: {len([v for v in preview_vars.keys() if '{' + v + '}' in current_text])}

💡 **ملاحظة:** هذه معاينة تجريبية باستخدام بيانات وهمية
"""

            self.bot.edit_message_text(
                text,
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown',
                reply_markup=keyboard
            )

        except Exception as e:
            logger.error(f"❌ خطأ في معاينة النص: {e}")
            self.bot.answer_callback_query(call.id, "❌ حدث خطأ")

    def export_texts(self, call):
        """تصدير جميع النصوص"""
        try:
            all_texts = text_manager.get_all_texts()

            # إنشاء ملف JSON للتصدير
            import json
            export_data = {
                "export_date": datetime.now().isoformat(),
                "version": "1.0",
                "texts": all_texts
            }

            # حفظ في ملف مؤقت
            filename = f"bot_texts_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)

            keyboard = types.InlineKeyboardMarkup()
            btn_back = types.InlineKeyboardButton(
                "🔙 العودة لإدارة النصوص",
                callback_data="admin_text"
            )
            keyboard.add(btn_back)

            text = f"""
📤 **تصدير النصوص**

✅ **تم إنشاء ملف التصدير بنجاح!**

📁 **اسم الملف:** `{filename}`
📊 **الإحصائيات:**
• عدد الأقسام: {len(all_texts)}
• إجمالي النصوص: {sum(len(section.get('keys', {})) if isinstance(section, dict) else 0 for section in all_texts.values())}
• تاريخ التصدير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

💾 **الملف محفوظ في مجلد البوت**

💡 **تلميح:** يمكنك استخدام هذا الملف لاستيراد النصوص في بوت آخر أو كنسخة احتياطية
"""

            self.bot.edit_message_text(
                text,
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown',
                reply_markup=keyboard
            )

        except Exception as e:
            logger.error(f"❌ خطأ في تصدير النصوص: {e}")
            self.bot.answer_callback_query(call.id, "❌ حدث خطأ في التصدير")

    def reset_texts_to_default(self, call):
        """إعادة تعيين النصوص للافتراضية"""
        try:
            keyboard = types.InlineKeyboardMarkup(row_width=2)
            btn_confirm = types.InlineKeyboardButton(
                "✅ تأكيد الإعادة",
                callback_data="text_edit_reset_confirm"
            )
            btn_cancel = types.InlineKeyboardButton(
                "❌ إلغاء",
                callback_data="admin_text"
            )
            keyboard.add(btn_confirm, btn_cancel)

            text = """
🔄 **إعادة تعيين النصوص الافتراضية**

⚠️ **تحذير مهم:**
هذه العملية ستقوم بحذف جميع النصوص المخصصة واستبدالها بالنصوص الافتراضية.

📋 **ما سيحدث:**
• سيتم حذف جميع التعديلات على النصوص
• ستعود جميع النصوص للحالة الافتراضية
• لا يمكن التراجع عن هذه العملية

💡 **نصيحة:** قم بتصدير النصوص الحالية أولاً كنسخة احتياطية

❓ **هل أنت متأكد من المتابعة؟**
"""

            self.bot.edit_message_text(
                text,
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown',
                reply_markup=keyboard
            )

        except Exception as e:
            logger.error(f"❌ خطأ في إعادة التعيين: {e}")
            self.bot.answer_callback_query(call.id, "❌ حدث خطأ")

    def confirm_reset_texts(self, call):
        """تأكيد إعادة تعيين النصوص"""
        try:
            # إعادة تحميل النصوص الافتراضية
            default_texts = text_manager.get_default_texts()

            # حفظ النصوص الافتراضية
            success = firebase_manager.save_bot_texts(default_texts)

            if success:
                # إعادة تحميل النصوص في المدير
                text_manager.reload_texts()

                keyboard = types.InlineKeyboardMarkup()
                btn_back = types.InlineKeyboardButton(
                    "🔙 العودة لإدارة النصوص",
                    callback_data="admin_text"
                )
                keyboard.add(btn_back)

                text = """
✅ **تم إعادة تعيين النصوص بنجاح!**

🔄 **ما تم:**
• تم حذف جميع النصوص المخصصة
• تم استعادة النصوص الافتراضية
• تم حفظ التغييرات في قاعدة البيانات

📝 **النصوص الآن:**
• جميع النصوص عادت للحالة الافتراضية
• يمكنك البدء في التخصيص من جديد
• تم تحديث جميع أقسام البوت

💡 **تلميح:** يمكنك الآن البدء في تخصيص النصوص حسب احتياجاتك
"""

                self.bot.edit_message_text(
                    text,
                    call.message.chat.id,
                    call.message.message_id,
                    parse_mode='Markdown',
                    reply_markup=keyboard
                )
            else:
                self.bot.answer_callback_query(call.id, "❌ فشل في إعادة التعيين")

        except Exception as e:
            logger.error(f"❌ خطأ في تأكيد إعادة التعيين: {e}")
            self.bot.answer_callback_query(call.id, "❌ حدث خطأ")

    def search_texts(self, call, search_query: str):
        """البحث في النصوص"""
        try:
            all_texts = text_manager.get_all_texts()
            search_results = []

            search_query_lower = search_query.lower()

            for section_key, section_data in all_texts.items():
                if isinstance(section_data, dict):
                    for text_key, text_value in section_data.items():
                        if isinstance(text_value, str):
                            if (search_query_lower in text_value.lower() or
                                search_query_lower in text_key.lower()):
                                search_results.append({
                                    "section": section_key,
                                    "key": text_key,
                                    "text": text_value[:100] + "..." if len(text_value) > 100 else text_value
                                })

            keyboard = types.InlineKeyboardMarkup(row_width=1)

            for result in search_results[:10]:  # عرض أول 10 نتائج
                btn = types.InlineKeyboardButton(
                    f"📝 {result['section']}.{result['key']}",
                    callback_data=f"text_edit_key_{result['section']}_{result['key']}"
                )
                keyboard.add(btn)

            btn_back = types.InlineKeyboardButton(
                "🔙 العودة لإدارة النصوص",
                callback_data="admin_text"
            )
            keyboard.add(btn_back)

            text = f"""
🔍 **نتائج البحث: "{search_query}"**

📊 **الإحصائيات:**
• عدد النتائج: {len(search_results)}
• النتائج المعروضة: {min(len(search_results), 10)}

📝 **النتائج:**
"""

            if search_results:
                for i, result in enumerate(search_results[:10], 1):
                    text += f"{i}. **{result['section']}.{result['key']}**\n"
                    text += f"   {result['text']}\n\n"

                if len(search_results) > 10:
                    text += f"... و {len(search_results) - 10} نتائج أخرى\n\n"
            else:
                text += "لم يتم العثور على نتائج\n\n"

            text += "💡 **تلميح:** انقر على أي نتيجة للانتقال إلى تحريرها"

            self.bot.edit_message_text(
                text,
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown',
                reply_markup=keyboard
            )

        except Exception as e:
            logger.error(f"❌ خطأ في البحث: {e}")
            self.bot.answer_callback_query(call.id, "❌ حدث خطأ في البحث")

    def is_user_editing(self, user_id: int) -> bool:
        """التحقق من وجود جلسة تحرير نشطة للمستخدم"""
        for session in self.editing_sessions.values():
            if session["user_id"] == user_id:
                return True
        return False

# دالة مساعدة لإنشاء مثيل محرر النصوص
def create_text_editor(bot):
    """إنشاء مثيل محرر النصوص"""
    return TextEditor(bot)
