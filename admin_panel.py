#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
لوحة التحكم الإدارية المتقدمة للبوت التعليمي
Advanced Admin Panel for Educational Bot

تدير جميع العمليات الإدارية والتحكم في البوت
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
from telebot import types
from firebase_manager import firebase_manager
from text_manager import text_manager
from button_manager import button_manager
from mcq_manager import mcq_manager
from content_manager import content_manager
from services_manager import services_manager
from educational_content_manager import educational_content_manager
from button_editor import create_button_editor
from bot_settings_manager import bot_settings_manager
from file_manager import file_manager
from backup_manager import backup_manager
from preview_manager import create_preview_manager
from permissions_manager import permissions_manager

logger = logging.getLogger(__name__)

class AdminPanel:
    """لوحة التحكم الإدارية المتقدمة"""
    
    def __init__(self, bot):
        self.bot = bot
        self.admin_states = {}  # حالات المشرفين
        self.temp_data = {}     # بيانات مؤقتة للعمليات
        self.button_editor = create_button_editor(bot)
        self.preview_manager = create_preview_manager(bot)
    
    def is_admin(self, user_id: int) -> bool:
        """التحقق من صلاحيات الإدارة"""
        try:
            admin_ids = firebase_manager.get_admin_ids()
            return user_id in admin_ids
        except Exception as e:
            logger.error(f"❌ خطأ في التحقق من صلاحيات الإدارة: {e}")
            return False
    
    def show_main_panel(self, chat_id: int, message_id: Optional[int] = None, user_id: Optional[int] = None):
        """عرض لوحة التحكم الرئيسية"""
        try:
            if not self.is_admin(user_id):
                error_text = text_manager.get_text("admin", "unauthorized", user_id=user_id)
                if message_id:
                    self.bot.edit_message_text(error_text, chat_id, message_id, parse_mode='Markdown')
                else:
                    self.bot.send_message(chat_id, error_text, parse_mode='Markdown')
                return
            
            # إنشاء النص والأزرار
            admin_text = self._create_admin_panel_text(user_id)
            keyboard = button_manager.create_keyboard("admin_panel", user_id)
            
            if message_id:
                self.bot.edit_message_text(
                    admin_text,
                    chat_id,
                    message_id,
                    parse_mode='Markdown',
                    reply_markup=keyboard
                )
            else:
                self.bot.send_message(
                    chat_id,
                    admin_text,
                    parse_mode='Markdown',
                    reply_markup=keyboard
                )
                
        except Exception as e:
            logger.error(f"❌ خطأ في عرض لوحة التحكم: {e}")
            error_msg = "❌ حدث خطأ في تحميل لوحة التحكم"
            if message_id:
                self.bot.edit_message_text(error_msg, chat_id, message_id)
            else:
                self.bot.send_message(chat_id, error_msg)
    
    def _create_admin_panel_text(self, user_id: int) -> str:
        """إنشاء نص لوحة التحكم"""
        try:
            # الحصول على معلومات المشرف
            admin_info = firebase_manager.get_admin_details(user_id)
            admin_name = admin_info.get('name', 'المشرف') if admin_info else 'المشرف'
            
            text = text_manager.get_text("admin", "panel_title") + "\n\n"
            text += text_manager.get_text("admin", "welcome", admin_name=admin_name) + "\n\n"
            text += text_manager.get_text("admin", "description") + "\n\n"
            
            # إضافة الأقسام المتاحة
            sections = [
                "📊 **الإحصائيات** - عرض إحصائيات شاملة للبوت",
                "👥 **إدارة المستخدمين** - عرض وإدارة المستخدمين",
                "💡 **الأفكار المقترحة** - مراجعة الأفكار الجديدة",
                "📚 **إدارة المحتوى** - تحديث المحتوى التعليمي",
                "🔤 **إدارة النصوص** - تخصيص نصوص البوت",
                "🔘 **إدارة الأزرار** - تخصيص أزرار البوت",
                "❓ **إدارة الأسئلة** - إدارة أسئلة MCQ",
                "👨‍💼 **إدارة المشرفين** - إضافة/إزالة المشرفين",
                "📢 **رسالة جماعية** - إرسال رسائل لجميع المستخدمين",
                "💾 **النسخ الاحتياطي** - إنشاء واستعادة النسخ الاحتياطية",
                "⚙️ **إعدادات البوت** - تخصيص إعدادات البوت",
                "📋 **سجلات النشاط** - عرض سجلات النشاط"
            ]
            
            for section in sections:
                text += section + "\n"
            
            text += f"\n🔐 **معرف المشرف:** `{user_id}`\n"
            text += f"📅 **التاريخ:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            
            return text
            
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء نص لوحة التحكم: {e}")
            return "🔧 **لوحة التحكم الإدارية**\n\nحدث خطأ في تحميل المحتوى."
    
    def handle_text_management(self, call):
        """إدارة النصوص"""
        try:
            keyboard = types.InlineKeyboardMarkup(row_width=2)
            
            # أزرار إدارة النصوص
            sections = [
                ("welcome", "نصوص الترحيب"),
                ("translation", "نصوص الترجمة"),
                ("educational_content", "نصوص المحتوى العلمي"),
                ("ministerial_materials", "نصوص المواد الوزارية"),
                ("services", "نصوص الخدمات"),
                ("suggest_idea", "نصوص اقتراح الأفكار"),
                ("admin", "نصوص الإدارة"),
                ("common", "النصوص العامة")
            ]
            
            for section_key, section_name in sections:
                btn = types.InlineKeyboardButton(
                    section_name,
                    callback_data=f"admin_text_section_{section_key}"
                )
                keyboard.add(btn)
            
            # زر العودة
            btn_back = types.InlineKeyboardButton("🔙 العودة للوحة التحكم", callback_data="admin_back")
            keyboard.add(btn_back)
            
            text = """
🔤 **إدارة النصوص**

اختر القسم الذي تريد تعديل نصوصه:

• **نصوص الترحيب** - رسائل البداية والترحيب
• **نصوص الترجمة** - رسائل قسم الترجمة
• **نصوص المحتوى العلمي** - رسائل المحتوى التعليمي
• **نصوص المواد الوزارية** - رسائل الأسئلة والاختبارات
• **نصوص الخدمات** - رسائل قسم الخدمات
• **نصوص اقتراح الأفكار** - رسائل اقتراح الأفكار
• **نصوص الإدارة** - رسائل لوحة التحكم
• **النصوص العامة** - الرسائل المشتركة

💡 **ملاحظة:** يمكنك استخدام متغيرات ديناميكية في النصوص مثل {user_name} و {date}
"""
            
            self.bot.edit_message_text(
                text,
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown',
                reply_markup=keyboard
            )
            
        except Exception as e:
            logger.error(f"❌ خطأ في إدارة النصوص: {e}")
            self.bot.answer_callback_query(call.id, "❌ حدث خطأ")
    
    def handle_button_management(self, call):
        """إدارة الأزرار المتقدمة"""
        try:
            # استخدام محرر الأزرار الجديد
            self.button_editor.show_button_management(call)

        except Exception as e:
            logger.error(f"❌ خطأ في إدارة الأزرار: {e}")
            self.bot.answer_callback_query(call.id, "❌ حدث خطأ")
    
    def handle_mcq_management(self, call):
        """إدارة أسئلة MCQ"""
        try:
            keyboard = types.InlineKeyboardMarkup(row_width=2)
            
            # الحصول على المواد المتاحة
            subjects = mcq_manager.get_subjects()
            
            for subject in subjects:
                btn = types.InlineKeyboardButton(
                    f"📚 {subject}",
                    callback_data=f"admin_mcq_subject_{subject}"
                )
                keyboard.add(btn)
            
            # أزرار إضافية
            btn_add_subject = types.InlineKeyboardButton("➕ إضافة مادة جديدة", callback_data="admin_mcq_add_subject")
            btn_settings = types.InlineKeyboardButton("⚙️ إعدادات الاختبارات", callback_data="admin_mcq_settings")
            btn_stats = types.InlineKeyboardButton("📊 إحصائيات الاختبارات", callback_data="admin_mcq_stats")
            btn_back = types.InlineKeyboardButton("🔙 العودة للوحة التحكم", callback_data="admin_back")
            
            keyboard.add(btn_add_subject)
            keyboard.add(btn_settings, btn_stats)
            keyboard.add(btn_back)
            
            text = f"""
❓ **إدارة أسئلة MCQ**

📚 **المواد المتاحة:** {len(subjects)}

اختر المادة التي تريد إدارة أسئلتها أو استخدم الخيارات أدناه:

🎯 **العمليات المتاحة:**
• إضافة/تعديل/حذف الأسئلة
• تحديد عدد الأسئلة في كل اختبار
• تفعيل/إلغاء خلط الأسئلة والإجابات
• تحديد وقت الاختبار ونسبة النجاح
• عرض إحصائيات الأداء

⚙️ **إعدادات متقدمة:**
• ترتيب الأسئلة (عشوائي أم مرتب)
• خلط الإجابات لمنع الحفظ
• إظهار/إخفاء الإجابات الصحيحة
• إضافة شروحات للأسئلة
"""
            
            self.bot.edit_message_text(
                text,
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown',
                reply_markup=keyboard
            )
            
        except Exception as e:
            logger.error(f"❌ خطأ في إدارة أسئلة MCQ: {e}")
            self.bot.answer_callback_query(call.id, "❌ حدث خطأ")
    
    def handle_content_management(self, call):
        """إدارة المحتوى"""
        try:
            keyboard = types.InlineKeyboardMarkup(row_width=2)
            
            # أزرار إدارة المحتوى
            btn_educational = types.InlineKeyboardButton("📖 المحتوى العلمي", callback_data="admin_content_educational")
            btn_ministerial = types.InlineKeyboardButton("🗂️ المواد الوزارية", callback_data="admin_content_ministerial")
            btn_services = types.InlineKeyboardButton("🛠️ الخدمات", callback_data="admin_content_services")
            btn_files = types.InlineKeyboardButton("📁 إدارة الملفات", callback_data="admin_content_files")
            btn_structure = types.InlineKeyboardButton("🏗️ هيكل الأقسام", callback_data="admin_content_structure")
            btn_preview = types.InlineKeyboardButton("👁️ معاينة المحتوى", callback_data="admin_content_preview")
            btn_back = types.InlineKeyboardButton("🔙 العودة للوحة التحكم", callback_data="admin_back")
            
            keyboard.add(btn_educational, btn_ministerial)
            keyboard.add(btn_services, btn_files)
            keyboard.add(btn_structure, btn_preview)
            keyboard.add(btn_back)
            
            text = """
📚 **إدارة المحتوى**

اختر نوع المحتوى الذي تريد إدارته:

📖 **المحتوى العلمي**
• إضافة/تعديل المراحل الدراسية
• إدارة المواد والكتب
• تنظيم الملزمات والموارد
• إنشاء أزرار تنقل ديناميكية

🗂️ **المواد الوزارية**
• إضافة أسئلة وزارية جديدة
• تنظيم الأسئلة حسب السنة والمادة
• إدارة ملفات PDF للأسئلة
• إعداد اختبارات تفاعلية

🛠️ **الخدمات**
• إضافة خدمات جديدة
• تحديد المختصين والأسعار
• إنشاء أزرار تواصل مخصصة
• إدارة طلبات الخدمات

📁 **إدارة الملفات**
• رفع وتنظيم الملفات
• إنشاء مجلدات ومكتبات
• ربط الملفات بالأقسام
• إدارة الصور والوسائط

🏗️ **هيكل الأقسام**
• إنشاء أقسام جديدة
• تنظيم التسلسل الهرمي
• تخصيص الأيقونات والألوان
• إعداد الصلاحيات والوصول
"""
            
            self.bot.edit_message_text(
                text,
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown',
                reply_markup=keyboard
            )
            
        except Exception as e:
            logger.error(f"❌ خطأ في إدارة المحتوى: {e}")
            self.bot.answer_callback_query(call.id, "❌ حدث خطأ")
    
    def handle_settings_management(self, call):
        """إدارة إعدادات البوت المتقدمة"""
        try:
            keyboard = types.InlineKeyboardMarkup(row_width=2)

            # الحصول على ملخص الإعدادات
            settings_summary = bot_settings_manager.get_settings_summary()

            # أزرار إدارة الإعدادات
            btn_behavior = types.InlineKeyboardButton("🎭 سلوك البوت", callback_data="admin_settings_behavior")
            btn_notifications = types.InlineKeyboardButton("🔔 الإشعارات", callback_data="admin_settings_notifications")
            btn_features = types.InlineKeyboardButton("🎯 المميزات", callback_data="admin_settings_features")
            btn_limits = types.InlineKeyboardButton("⚖️ الحدود والقيود", callback_data="admin_settings_limits")
            btn_appearance = types.InlineKeyboardButton("🎨 المظهر", callback_data="admin_settings_appearance")
            btn_security = types.InlineKeyboardButton("🔒 الأمان", callback_data="admin_settings_security")
            btn_performance = types.InlineKeyboardButton("⚡ الأداء", callback_data="admin_settings_performance")
            btn_backup = types.InlineKeyboardButton("💾 النسخ الاحتياطي", callback_data="admin_settings_backup")
            btn_analytics = types.InlineKeyboardButton("📊 التحليلات", callback_data="admin_settings_analytics")
            btn_maintenance = types.InlineKeyboardButton("🔧 الصيانة", callback_data="admin_settings_maintenance")
            btn_export = types.InlineKeyboardButton("📤 تصدير الإعدادات", callback_data="admin_settings_export")
            btn_import = types.InlineKeyboardButton("📥 استيراد الإعدادات", callback_data="admin_settings_import")
            btn_reset = types.InlineKeyboardButton("🔄 إعادة تعيين", callback_data="admin_settings_reset")
            btn_back = types.InlineKeyboardButton("🔙 العودة للوحة التحكم", callback_data="admin_back")

            keyboard.add(btn_behavior, btn_notifications)
            keyboard.add(btn_features, btn_limits)
            keyboard.add(btn_appearance, btn_security)
            keyboard.add(btn_performance, btn_backup)
            keyboard.add(btn_analytics, btn_maintenance)
            keyboard.add(btn_export, btn_import)
            keyboard.add(btn_reset)
            keyboard.add(btn_back)

            maintenance_status = "🔴 نشط" if bot_settings_manager.get_setting("maintenance", "maintenance_mode", False) else "🟢 غير نشط"

            text = f"""
⚙️ **إدارة إعدادات البوت المتقدمة**

📊 **الحالة العامة:**
• إجمالي الفئات: {settings_summary.get('total_categories', 0)}
• المميزات المفعلة: {settings_summary.get('enabled_features', 0)}
• المميزات المعطلة: {settings_summary.get('disabled_features', 0)}
• وضع الصيانة: {maintenance_status}
• آخر تحديث: {settings_summary.get('last_updated', 'غير محدد')[:10]}
• الإصدار: {settings_summary.get('version', 'غير محدد')}

🎛️ **فئات الإعدادات:**

🎭 **سلوك البوت**
• طريقة عرض الرسائل (تعديل/جديدة)
• تأخير الكتابة والاستجابة
• الحذف التلقائي للرسائل
• إعدادات التفاعل مع المستخدمين

🔔 **الإشعارات**
• إشعارات callback query
• إشعارات النجاح والأخطاء
• إشعارات المشرفين
• تخصيص نصوص الإشعارات

🎯 **المميزات**
• تفعيل/إلغاء الترجمة
• تفعيل/إلغاء أسئلة MCQ
• تفعيل/إلغاء رفع الملفات
• تفعيل/إلغاء اقتراح الأفكار

⚖️ **الحدود والقيود**
• حد حجم الملفات المرفوعة
• حد طول النصوص
• عدد الأسئلة في الاختبار
• معدل الطلبات المسموح

🎨 **المظهر**
• استخدام الرموز التعبيرية
• تفعيل Markdown
• نمط الأزرار ونظام الألوان

🔒 **الأمان**
• حماية من الرسائل المزعجة
• حماية من الفيضان
• تسجيل أنشطة المستخدمين

⚡ **الأداء**
• إعدادات التخزين المؤقت
• تحسين الاستجابة
• إدارة الذاكرة

💾 **النسخ الاحتياطي**
• النسخ التلقائي
• إعدادات الاستعادة
• ضغط وتشفير النسخ

📊 **التحليلات**
• تتبع تفاعل المستخدمين
• إحصائيات الاستخدام
• تقارير الأداء

🔧 **الصيانة**
• وضع الصيانة
• الصيانة المجدولة
• إشعارات الصيانة

🔧 **أدوات الإدارة:**
• **تصدير الإعدادات** - حفظ نسخة من الإعدادات
• **استيراد الإعدادات** - استعادة إعدادات محفوظة
• **إعادة تعيين** - العودة للإعدادات الافتراضية

اختر الفئة التي تريد تخصيصها:
"""

            self.bot.edit_message_text(
                text,
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown',
                reply_markup=keyboard
            )

        except Exception as e:
            logger.error(f"❌ خطأ في إدارة الإعدادات: {e}")
            self.bot.answer_callback_query(call.id, "❌ حدث خطأ")

    def handle_educational_content_management(self, call):
        """إدارة المحتوى التعليمي المتقدم"""
        try:
            keyboard = types.InlineKeyboardMarkup(row_width=2)

            # الحصول على المراحل المتاحة
            stages = educational_content_manager.get_stages()

            for stage in stages:
                stage_name = stage["name"]
                stage_data = stage["data"]
                subjects_count = len(stage_data.get("subjects", {}))

                btn = types.InlineKeyboardButton(
                    f"{stage_data.get('icon', '📚')} {stage_name} ({subjects_count})",
                    callback_data=f"admin_edu_stage_{stage_name}"
                )
                keyboard.add(btn)

            # أزرار إضافية
            btn_add_stage = types.InlineKeyboardButton("➕ إضافة مرحلة جديدة", callback_data="admin_edu_add_stage")
            btn_structure = types.InlineKeyboardButton("🏗️ إدارة الهيكل", callback_data="admin_edu_structure")
            btn_navigation = types.InlineKeyboardButton("🧭 أزرار التنقل", callback_data="admin_edu_navigation")
            btn_statistics = types.InlineKeyboardButton("📊 إحصائيات المحتوى", callback_data="admin_edu_statistics")
            btn_settings = types.InlineKeyboardButton("⚙️ إعدادات المحتوى", callback_data="admin_edu_settings")
            btn_import_export = types.InlineKeyboardButton("📦 استيراد/تصدير", callback_data="admin_edu_import_export")
            btn_back = types.InlineKeyboardButton("🔙 العودة لإدارة المحتوى", callback_data="admin_content")

            keyboard.add(btn_add_stage)
            keyboard.add(btn_structure, btn_navigation)
            keyboard.add(btn_statistics, btn_settings)
            keyboard.add(btn_import_export)
            keyboard.add(btn_back)

            # الحصول على الإحصائيات
            stats = educational_content_manager.get_content_statistics()

            text = f"""
📖 **إدارة المحتوى التعليمي التفاعلي**

📊 **الإحصائيات العامة:**
• المراحل الدراسية: {stats.get('total_stages', 0)}
• المواد الدراسية: {stats.get('total_subjects', 0)}
• الفصول: {stats.get('total_chapters', 0)}
• الدروس: {stats.get('total_lessons', 0)}
• الموارد التعليمية: {stats.get('total_resources', 0)}

📚 **المراحل المتاحة:**
"""

            if stages:
                for stage in stages[:3]:  # عرض أول 3 مراحل
                    stage_data = stage["data"]
                    subjects_count = len(stage_data.get("subjects", {}))
                    text += f"• {stage_data.get('icon', '📚')} **{stage['name']}**\n"
                    text += f"  المواد: {subjects_count} | الصعوبة: {stage_data.get('difficulty', 'غير محدد')}\n"
                    text += f"  الوصف: {stage_data.get('description', 'لا يوجد وصف')}\n\n"

                if len(stages) > 3:
                    text += f"... و {len(stages) - 3} مراحل أخرى\n\n"
            else:
                text += "لا توجد مراحل دراسية حالياً\n\n"

            text += """
🎯 **العمليات المتاحة:**

📚 **إدارة المراحل والمواد:**
• إضافة/تعديل/حذف المراحل الدراسية
• إدارة المواد والمناهج الدراسية
• تنظيم الفصول والدروس
• إضافة الموارد التعليمية (فيديوهات، ملفات، تمارين)

🏗️ **إدارة الهيكل:**
• تخصيص التسلسل الهرمي للمحتوى
• تحديد طريقة العرض والتنظيم
• إعداد الأيقونات والألوان
• تنظيم الترتيب والأولويات

🧭 **أزرار التنقل:**
• إنشاء أزرار تنقل ديناميكية
• تخصيص مسارات التنقل
• إعداد الروابط السريعة
• تفعيل نظام البحث والفهرسة

📊 **التقارير والإحصائيات:**
• إحصائيات استخدام المحتوى
• تقارير تقدم المستخدمين
• تحليل المحتوى الأكثر استخداماً
• إحصائيات الموارد والملفات

⚙️ **الإعدادات المتقدمة:**
• تفعيل/إلغاء تتبع التقدم
• إظهار/إخفاء مؤشرات الصعوبة
• تفعيل نظام المتطلبات المسبقة
• إعدادات الوصول والصلاحيات

📦 **الاستيراد والتصدير:**
• استيراد محتوى من ملفات خارجية
• تصدير المحتوى للمشاركة
• نسخ احتياطية للمحتوى
• مزامنة مع منصات أخرى

اختر المرحلة أو العملية المطلوبة:
"""

            self.bot.edit_message_text(
                text,
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown',
                reply_markup=keyboard
            )

        except Exception as e:
            logger.error(f"❌ خطأ في إدارة المحتوى التعليمي: {e}")
            self.bot.answer_callback_query(call.id, "❌ حدث خطأ")

    def handle_services_management(self, call):
        """إدارة الخدمات المتقدمة"""
        try:
            keyboard = types.InlineKeyboardMarkup(row_width=2)

            # الحصول على فئات الخدمات المتاحة
            categories = services_manager.get_service_categories()

            for category in categories:
                category_name = category["name"]
                category_data = category["data"]
                services_count = len(category_data.get("services", {}))

                btn = types.InlineKeyboardButton(
                    f"{category_data.get('icon', '🛠️')} {category_name} ({services_count})",
                    callback_data=f"admin_service_category_{category_name}"
                )
                keyboard.add(btn)

            # أزرار إضافية
            btn_add_category = types.InlineKeyboardButton("➕ إضافة فئة جديدة", callback_data="admin_service_add_category")
            btn_specialists = types.InlineKeyboardButton("👥 إدارة المختصين", callback_data="admin_service_specialists")
            btn_orders = types.InlineKeyboardButton("📋 طلبات الخدمات", callback_data="admin_service_orders")
            btn_statistics = types.InlineKeyboardButton("📊 إحصائيات الخدمات", callback_data="admin_service_statistics")
            btn_settings = types.InlineKeyboardButton("⚙️ إعدادات الخدمات", callback_data="admin_service_settings")
            btn_back = types.InlineKeyboardButton("🔙 العودة لإدارة المحتوى", callback_data="admin_content")

            keyboard.add(btn_add_category)
            keyboard.add(btn_specialists, btn_orders)
            keyboard.add(btn_statistics, btn_settings)
            keyboard.add(btn_back)

            # الحصول على الإحصائيات
            stats = services_manager.services_data.get("statistics", {})

            text = f"""
🛠️ **إدارة الخدمات المتقدمة**

📊 **الإحصائيات العامة:**
• فئات الخدمات: {len(categories)}
• إجمالي الخدمات: {stats.get('total_services', 0)}
• المختصين النشطين: {stats.get('active_specialists', 0)}
• إجمالي الطلبات: {stats.get('total_orders', 0)}
• الطلبات المكتملة: {stats.get('completed_orders', 0)}

🎯 **العمليات المتاحة:**

📂 **إدارة الفئات والخدمات:**
• إضافة/تعديل/حذف فئات الخدمات
• إدارة الخدمات وتفاصيلها
• تحديد الأسعار والباقات
• إضافة أمثلة الأعمال والمعرض
• إنشاء أزرار تواصل مخصصة

👥 **إدارة المختصين:**
• إضافة/تعديل بيانات المختصين
• تحديد التخصصات والخبرات
• إدارة معلومات التواصل
• نظام التقييمات والمراجعات
• متابعة الأداء والإنتاجية

📋 **إدارة الطلبات:**
• عرض الطلبات الجديدة والمعلقة
• تعيين المختصين للطلبات
• متابعة حالة التنفيذ
• إدارة التواصل مع العملاء
• تحديث حالة الطلبات

📊 **التقارير والإحصائيات:**
• إحصائيات الأداء والمبيعات
• تقارير رضا العملاء
• تحليل أداء المختصين
• إحصائيات الطلبات والإيرادات

⚙️ **الإعدادات المتقدمة:**
• تفعيل/إلغاء طلب عروض الأسعار
• إظهار/إخفاء أمثلة الأعمال
• تفعيل نظام التقييمات
• إعدادات طرق التواصل والدفع
• إعدادات الموافقة التلقائية

اختر الفئة أو العملية المطلوبة:
"""

            self.bot.edit_message_text(
                text,
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown',
                reply_markup=keyboard
            )

        except Exception as e:
            logger.error(f"❌ خطأ في إدارة الخدمات: {e}")
            self.bot.answer_callback_query(call.id, "❌ حدث خطأ")

    def handle_mcq_subject_management(self, call, subject_name: str):
        """إدارة أسئلة مادة محددة"""
        try:
            keyboard = types.InlineKeyboardMarkup(row_width=2)

            # الحصول على معلومات المادة
            subject_info = mcq_manager.get_subject_info(subject_name)
            if not subject_info:
                self.bot.answer_callback_query(call.id, "❌ المادة غير موجودة")
                return

            questions = subject_info.get("questions", [])
            settings = subject_info.get("settings", {})

            # أزرار إدارة الأسئلة
            btn_view_questions = types.InlineKeyboardButton(
                f"📋 عرض الأسئلة ({len(questions)})",
                callback_data=f"admin_mcq_view_{subject_name}"
            )
            btn_add_question = types.InlineKeyboardButton(
                "➕ إضافة سؤال جديد",
                callback_data=f"admin_mcq_add_{subject_name}"
            )
            btn_import_questions = types.InlineKeyboardButton(
                "📥 استيراد أسئلة",
                callback_data=f"admin_mcq_import_{subject_name}"
            )
            btn_export_questions = types.InlineKeyboardButton(
                "📤 تصدير أسئلة",
                callback_data=f"admin_mcq_export_{subject_name}"
            )
            btn_settings = types.InlineKeyboardButton(
                "⚙️ إعدادات المادة",
                callback_data=f"admin_mcq_settings_{subject_name}"
            )
            btn_test_preview = types.InlineKeyboardButton(
                "🎯 معاينة اختبار",
                callback_data=f"admin_mcq_test_{subject_name}"
            )
            btn_statistics = types.InlineKeyboardButton(
                "📊 إحصائيات الأداء",
                callback_data=f"admin_mcq_stats_{subject_name}"
            )
            btn_back = types.InlineKeyboardButton(
                "🔙 العودة لقائمة المواد",
                callback_data="admin_mcq"
            )

            keyboard.add(btn_view_questions, btn_add_question)
            keyboard.add(btn_import_questions, btn_export_questions)
            keyboard.add(btn_settings, btn_test_preview)
            keyboard.add(btn_statistics)
            keyboard.add(btn_back)

            text = f"""
📚 **إدارة أسئلة: {subject_name}**

📊 **إحصائيات المادة:**
• عدد الأسئلة: {len(questions)}
• أسئلة لكل اختبار: {settings.get('questions_per_test', 10)}
• وقت الاختبار: {settings.get('time_limit', 15)} دقيقة
• نسبة النجاح: {settings.get('passing_score', 60)}%

⚙️ **الإعدادات الحالية:**
• خلط الأسئلة: {"✅" if settings.get('shuffle_questions', True) else "❌"}
• خلط الإجابات: {"✅" if settings.get('shuffle_answers', True) else "❌"}
• إظهار الإجابة الصحيحة: {"✅" if settings.get('show_correct_answer', True) else "❌"}
• إظهار الشرح: {"✅" if settings.get('show_explanation', True) else "❌"}

🛠️ **العمليات المتاحة:**
• **عرض الأسئلة** - استعراض وتحرير الأسئلة الموجودة
• **إضافة سؤال جديد** - إنشاء سؤال جديد بالخيارات والشرح
• **استيراد أسئلة** - تحميل أسئلة من ملف Excel أو JSON
• **تصدير أسئلة** - حفظ الأسئلة في ملف للمشاركة
• **إعدادات المادة** - تخصيص إعدادات الاختبارات
• **معاينة اختبار** - تجربة الاختبار كما يراه المستخدم
• **إحصائيات الأداء** - عرض نتائج وتحليل الأداء
"""

            self.bot.edit_message_text(
                text,
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown',
                reply_markup=keyboard
            )

        except Exception as e:
            logger.error(f"❌ خطأ في إدارة أسئلة المادة: {e}")
            self.bot.answer_callback_query(call.id, "❌ حدث خطأ")

    def handle_mcq_settings(self, call, subject_name: str):
        """إدارة إعدادات أسئلة MCQ"""
        try:
            subject_info = mcq_manager.get_subject_info(subject_name)
            if not subject_info:
                self.bot.answer_callback_query(call.id, "❌ المادة غير موجودة")
                return

            settings = subject_info.get("settings", {})
            keyboard = types.InlineKeyboardMarkup(row_width=2)

            # أزرار تغيير الإعدادات
            btn_questions_count = types.InlineKeyboardButton(
                f"📊 عدد الأسئلة: {settings.get('questions_per_test', 10)}",
                callback_data=f"admin_mcq_set_count_{subject_name}"
            )
            btn_time_limit = types.InlineKeyboardButton(
                f"⏱️ الوقت: {settings.get('time_limit', 15)} دقيقة",
                callback_data=f"admin_mcq_set_time_{subject_name}"
            )
            btn_passing_score = types.InlineKeyboardButton(
                f"🎯 نسبة النجاح: {settings.get('passing_score', 60)}%",
                callback_data=f"admin_mcq_set_score_{subject_name}"
            )

            # أزرار التبديل
            shuffle_q = "✅" if settings.get('shuffle_questions', True) else "❌"
            btn_shuffle_questions = types.InlineKeyboardButton(
                f"{shuffle_q} خلط الأسئلة",
                callback_data=f"admin_mcq_toggle_shuffle_q_{subject_name}"
            )

            shuffle_a = "✅" if settings.get('shuffle_answers', True) else "❌"
            btn_shuffle_answers = types.InlineKeyboardButton(
                f"{shuffle_a} خلط الإجابات",
                callback_data=f"admin_mcq_toggle_shuffle_a_{subject_name}"
            )

            show_correct = "✅" if settings.get('show_correct_answer', True) else "❌"
            btn_show_correct = types.InlineKeyboardButton(
                f"{show_correct} إظهار الإجابة الصحيحة",
                callback_data=f"admin_mcq_toggle_correct_{subject_name}"
            )

            show_explanation = "✅" if settings.get('show_explanation', True) else "❌"
            btn_show_explanation = types.InlineKeyboardButton(
                f"{show_explanation} إظهار الشرح",
                callback_data=f"admin_mcq_toggle_explanation_{subject_name}"
            )

            btn_save = types.InlineKeyboardButton(
                "💾 حفظ الإعدادات",
                callback_data=f"admin_mcq_save_settings_{subject_name}"
            )
            btn_reset = types.InlineKeyboardButton(
                "🔄 إعادة تعيين افتراضية",
                callback_data=f"admin_mcq_reset_settings_{subject_name}"
            )
            btn_back = types.InlineKeyboardButton(
                f"🔙 العودة لإدارة {subject_name}",
                callback_data=f"admin_mcq_subject_{subject_name}"
            )

            keyboard.add(btn_questions_count, btn_time_limit)
            keyboard.add(btn_passing_score)
            keyboard.add(btn_shuffle_questions)
            keyboard.add(btn_shuffle_answers)
            keyboard.add(btn_show_correct)
            keyboard.add(btn_show_explanation)
            keyboard.add(btn_save, btn_reset)
            keyboard.add(btn_back)

            text = f"""
⚙️ **إعدادات اختبارات: {subject_name}**

🎯 **إعدادات الاختبار:**
• **عدد الأسئلة لكل اختبار:** {settings.get('questions_per_test', 10)} سؤال
• **الوقت المحدد:** {settings.get('time_limit', 15)} دقيقة
• **نسبة النجاح المطلوبة:** {settings.get('passing_score', 60)}%

🔀 **إعدادات العشوائية:**
• **خلط ترتيب الأسئلة:** {shuffle_q}
• **خلط ترتيب الإجابات:** {shuffle_a}

📋 **إعدادات العرض:**
• **إظهار الإجابة الصحيحة:** {show_correct}
• **إظهار شرح الإجابة:** {show_explanation}

💡 **تلميحات:**
• خلط الأسئلة يمنع حفظ ترتيب الأسئلة
• خلط الإجابات يمنع حفظ مواضع الإجابات الصحيحة
• إظهار الشرح يساعد في التعلم من الأخطاء
• يمكن تخصيص هذه الإعدادات لكل مادة على حدة

🔧 **انقر على أي إعداد لتغييره:**
"""

            self.bot.edit_message_text(
                text,
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown',
                reply_markup=keyboard
            )

        except Exception as e:
            logger.error(f"❌ خطأ في إدارة إعدادات MCQ: {e}")
            self.bot.answer_callback_query(call.id, "❌ حدث خطأ")

    def handle_service_category_management(self, call, category_name: str):
        """إدارة فئة خدمات محددة"""
        try:
            keyboard = types.InlineKeyboardMarkup(row_width=2)

            # الحصول على خدمات الفئة
            services = services_manager.get_category_services(category_name)

            for service in services:
                service_name = service["name"]
                service_data = service["data"]

                btn = types.InlineKeyboardButton(
                    f"{service_data.get('icon', '🔧')} {service_name}",
                    callback_data=f"admin_service_edit_{category_name}_{service_name}"
                )
                keyboard.add(btn)

            # أزرار إدارة الفئة
            btn_add_service = types.InlineKeyboardButton(
                "➕ إضافة خدمة جديدة",
                callback_data=f"admin_service_add_{category_name}"
            )
            btn_edit_category = types.InlineKeyboardButton(
                "✏️ تعديل الفئة",
                callback_data=f"admin_service_edit_category_{category_name}"
            )
            btn_category_settings = types.InlineKeyboardButton(
                "⚙️ إعدادات الفئة",
                callback_data=f"admin_service_category_settings_{category_name}"
            )
            btn_back = types.InlineKeyboardButton(
                "🔙 العودة لإدارة الخدمات",
                callback_data="admin_content_services"
            )

            keyboard.add(btn_add_service)
            keyboard.add(btn_edit_category, btn_category_settings)
            keyboard.add(btn_back)

            # الحصول على معلومات الفئة
            categories = services_manager.get_service_categories()
            category_data = None
            for cat in categories:
                if cat["name"] == category_name:
                    category_data = cat["data"]
                    break

            if not category_data:
                self.bot.answer_callback_query(call.id, "❌ الفئة غير موجودة")
                return

            text = f"""
🛠️ **إدارة فئة: {category_name}**

📋 **معلومات الفئة:**
• الرمز: {category_data.get('icon', '🛠️')}
• الوصف: {category_data.get('description', 'لا يوجد وصف')}
• عدد الخدمات: {len(services)}
• الحالة: {"نشطة" if category_data.get('active', True) else "غير نشطة"}

🔧 **الخدمات المتاحة:**
"""

            if services:
                for service in services[:5]:  # عرض أول 5 خدمات
                    service_data = service["data"]
                    text += f"• {service_data.get('icon', '🔧')} **{service['name']}**\n"
                    text += f"  السعر: {service_data.get('price_range', 'غير محدد')}\n"
                    text += f"  التسليم: {service_data.get('delivery_time', 'غير محدد')}\n\n"

                if len(services) > 5:
                    text += f"... و {len(services) - 5} خدمات أخرى\n\n"
            else:
                text += "لا توجد خدمات في هذه الفئة حالياً\n\n"

            text += """
🎯 **العمليات المتاحة:**
• **إضافة خدمة جديدة** - إنشاء خدمة جديدة في هذه الفئة
• **تعديل الفئة** - تحديث معلومات وإعدادات الفئة
• **إعدادات الفئة** - تخصيص إعدادات خاصة بالفئة

💡 **تلميح:** انقر على أي خدمة لتحريرها أو عرض تفاصيلها
"""

            self.bot.edit_message_text(
                text,
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown',
                reply_markup=keyboard
            )

        except Exception as e:
            logger.error(f"❌ خطأ في إدارة فئة الخدمات: {e}")
            self.bot.answer_callback_query(call.id, "❌ حدث خطأ")

    def handle_specialists_management(self, call):
        """إدارة المختصين"""
        try:
            keyboard = types.InlineKeyboardMarkup(row_width=2)

            # الحصول على قائمة المختصين
            specialists = services_manager.specialists_data

            for specialist_id, specialist_data in specialists.items():
                if specialist_data.get("active", True):
                    name = specialist_data.get("name", specialist_id)
                    rating = specialist_data.get("ratings", {}).get("average", 0)

                    btn = types.InlineKeyboardButton(
                        f"👤 {name} ⭐{rating:.1f}",
                        callback_data=f"admin_specialist_edit_{specialist_id}"
                    )
                    keyboard.add(btn)

            # أزرار إضافية
            btn_add_specialist = types.InlineKeyboardButton(
                "➕ إضافة مختص جديد",
                callback_data="admin_specialist_add"
            )
            btn_specialist_stats = types.InlineKeyboardButton(
                "📊 إحصائيات المختصين",
                callback_data="admin_specialist_stats"
            )
            btn_specialist_settings = types.InlineKeyboardButton(
                "⚙️ إعدادات المختصين",
                callback_data="admin_specialist_settings"
            )
            btn_back = types.InlineKeyboardButton(
                "🔙 العودة لإدارة الخدمات",
                callback_data="admin_content_services"
            )

            keyboard.add(btn_add_specialist)
            keyboard.add(btn_specialist_stats, btn_specialist_settings)
            keyboard.add(btn_back)

            active_specialists = len([s for s in specialists.values() if s.get("active", True)])
            total_reviews = sum(s.get("ratings", {}).get("total_reviews", 0) for s in specialists.values())
            avg_rating = sum(s.get("ratings", {}).get("average", 0) for s in specialists.values()) / len(specialists) if specialists else 0

            text = f"""
👥 **إدارة المختصين**

📊 **الإحصائيات العامة:**
• المختصين النشطين: {active_specialists}
• إجمالي المختصين: {len(specialists)}
• إجمالي التقييمات: {total_reviews}
• متوسط التقييم العام: {avg_rating:.1f} ⭐

👤 **المختصين المسجلين:**
"""

            if specialists:
                for specialist_id, specialist_data in list(specialists.items())[:5]:
                    if specialist_data.get("active", True):
                        name = specialist_data.get("name", specialist_id)
                        title = specialist_data.get("title", "مختص")
                        rating = specialist_data.get("ratings", {}).get("average", 0)
                        reviews = specialist_data.get("ratings", {}).get("total_reviews", 0)

                        text += f"• **{name}** - {title}\n"
                        text += f"  التقييم: {rating:.1f} ⭐ ({reviews} تقييم)\n"
                        text += f"  الخبرة: {specialist_data.get('experience', 'غير محدد')}\n\n"

                if len(specialists) > 5:
                    text += f"... و {len(specialists) - 5} مختصين آخرين\n\n"
            else:
                text += "لا يوجد مختصين مسجلين حالياً\n\n"

            text += """
🎯 **العمليات المتاحة:**
• **إضافة مختص جديد** - تسجيل مختص جديد في المنصة
• **إحصائيات المختصين** - عرض تقارير الأداء والإنتاجية
• **إعدادات المختصين** - تخصيص إعدادات عامة للمختصين

💡 **تلميح:** انقر على أي مختص لعرض ملفه الشخصي وإدارة بياناته
"""

            self.bot.edit_message_text(
                text,
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown',
                reply_markup=keyboard
            )

        except Exception as e:
            logger.error(f"❌ خطأ في إدارة المختصين: {e}")
            self.bot.answer_callback_query(call.id, "❌ حدث خطأ")

    def handle_educational_stage_management(self, call, stage_name: str):
        """إدارة مرحلة دراسية محددة"""
        try:
            keyboard = types.InlineKeyboardMarkup(row_width=2)

            # الحصول على مواد المرحلة
            subjects = educational_content_manager.get_stage_subjects(stage_name)

            for subject in subjects:
                subject_name = subject["name"]
                subject_data = subject["data"]
                chapters_count = len(subject_data.get("chapters", {}))

                btn = types.InlineKeyboardButton(
                    f"{subject_data.get('icon', '📖')} {subject_name} ({chapters_count})",
                    callback_data=f"admin_edu_subject_{stage_name}_{subject_name}"
                )
                keyboard.add(btn)

            # أزرار إدارة المرحلة
            btn_add_subject = types.InlineKeyboardButton(
                "➕ إضافة مادة جديدة",
                callback_data=f"admin_edu_add_subject_{stage_name}"
            )
            btn_edit_stage = types.InlineKeyboardButton(
                "✏️ تعديل المرحلة",
                callback_data=f"admin_edu_edit_stage_{stage_name}"
            )
            btn_stage_settings = types.InlineKeyboardButton(
                "⚙️ إعدادات المرحلة",
                callback_data=f"admin_edu_stage_settings_{stage_name}"
            )
            btn_stage_stats = types.InlineKeyboardButton(
                "📊 إحصائيات المرحلة",
                callback_data=f"admin_edu_stage_stats_{stage_name}"
            )
            btn_back = types.InlineKeyboardButton(
                "🔙 العودة لإدارة المحتوى التعليمي",
                callback_data="admin_content_educational"
            )

            keyboard.add(btn_add_subject)
            keyboard.add(btn_edit_stage, btn_stage_settings)
            keyboard.add(btn_stage_stats)
            keyboard.add(btn_back)

            # الحصول على معلومات المرحلة
            stages = educational_content_manager.get_stages()
            stage_data = None
            for stage in stages:
                if stage["name"] == stage_name:
                    stage_data = stage["data"]
                    break

            if not stage_data:
                self.bot.answer_callback_query(call.id, "❌ المرحلة غير موجودة")
                return

            # حساب إحصائيات المرحلة
            total_chapters = 0
            total_lessons = 0
            total_resources = 0

            for subject in subjects:
                subject_data = subject["data"]
                chapters = subject_data.get("chapters", {})
                total_chapters += len(chapters)

                for chapter_data in chapters.values():
                    lessons = chapter_data.get("lessons", {})
                    total_lessons += len(lessons)

                    for lesson_data in lessons.values():
                        resources = lesson_data.get("resources", {})
                        for resource_list in resources.values():
                            total_resources += len(resource_list)

            text = f"""
📚 **إدارة مرحلة: {stage_name}**

📋 **معلومات المرحلة:**
• الرمز: {stage_data.get('icon', '📚')}
• الوصف: {stage_data.get('description', 'لا يوجد وصف')}
• اللون: {stage_data.get('color', 'افتراضي')}
• الترتيب: {stage_data.get('order', 'غير محدد')}
• الحالة: {"نشطة" if stage_data.get('active', True) else "غير نشطة"}

📊 **الإحصائيات:**
• عدد المواد: {len(subjects)}
• عدد الفصول: {total_chapters}
• عدد الدروس: {total_lessons}
• عدد الموارد: {total_resources}
• الساعات المقدرة: {stage_data.get('estimated_hours', 'غير محدد')}

📖 **المواد الدراسية:**
"""

            if subjects:
                for subject in subjects[:5]:  # عرض أول 5 مواد
                    subject_data = subject["data"]
                    chapters_count = len(subject_data.get("chapters", {}))
                    text += f"• {subject_data.get('icon', '📖')} **{subject['name']}**\n"
                    text += f"  الفصول: {chapters_count} | الصعوبة: {subject_data.get('difficulty', 'غير محدد')}\n"
                    text += f"  الساعات: {subject_data.get('estimated_hours', 'غير محدد')} | اللون: {subject_data.get('color', 'افتراضي')}\n\n"

                if len(subjects) > 5:
                    text += f"... و {len(subjects) - 5} مواد أخرى\n\n"
            else:
                text += "لا توجد مواد في هذه المرحلة حالياً\n\n"

            text += """
🎯 **العمليات المتاحة:**
• **إضافة مادة جديدة** - إنشاء مادة دراسية جديدة في هذه المرحلة
• **تعديل المرحلة** - تحديث معلومات وإعدادات المرحلة
• **إعدادات المرحلة** - تخصيص إعدادات خاصة بالمرحلة
• **إحصائيات المرحلة** - عرض تقارير مفصلة عن المرحلة

💡 **تلميح:** انقر على أي مادة لإدارة فصولها ودروسها
"""

            self.bot.edit_message_text(
                text,
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown',
                reply_markup=keyboard
            )

        except Exception as e:
            logger.error(f"❌ خطأ في إدارة المرحلة الدراسية: {e}")
            self.bot.answer_callback_query(call.id, "❌ حدث خطأ")

    def handle_behavior_settings(self, call):
        """إدارة إعدادات سلوك البوت"""
        try:
            keyboard = types.InlineKeyboardMarkup(row_width=2)

            # الحصول على الإعدادات الحالية
            behavior_settings = bot_settings_manager.get_category_settings("behavior")

            # أزرار التحكم في الإعدادات
            message_mode = behavior_settings.get("message_display_mode", "edit")
            btn_message_mode = types.InlineKeyboardButton(
                f"📝 طريقة العرض: {message_mode}",
                callback_data="admin_settings_toggle_message_mode"
            )

            typing_delay = behavior_settings.get("typing_delay", 1.0)
            btn_typing_delay = types.InlineKeyboardButton(
                f"⏱️ تأخير الكتابة: {typing_delay}ث",
                callback_data="admin_settings_edit_typing_delay"
            )

            auto_delete = behavior_settings.get("auto_delete_messages", False)
            btn_auto_delete = types.InlineKeyboardButton(
                f"🗑️ حذف تلقائي: {'مفعل' if auto_delete else 'معطل'}",
                callback_data="admin_settings_toggle_auto_delete"
            )

            callback_notifications = behavior_settings.get("use_callback_notifications", True)
            btn_callback_notifications = types.InlineKeyboardButton(
                f"🔔 إشعارات الأزرار: {'مفعل' if callback_notifications else 'معطل'}",
                callback_data="admin_settings_toggle_callback_notifications"
            )

            user_feedback = behavior_settings.get("enable_user_feedback", True)
            btn_user_feedback = types.InlineKeyboardButton(
                f"💬 ملاحظات المستخدمين: {'مفعل' if user_feedback else 'معطل'}",
                callback_data="admin_settings_toggle_user_feedback"
            )

            processing_messages = behavior_settings.get("show_processing_messages", True)
            btn_processing_messages = types.InlineKeyboardButton(
                f"⏳ رسائل المعالجة: {'مفعل' if processing_messages else 'معطل'}",
                callback_data="admin_settings_toggle_processing_messages"
            )

            btn_reset_behavior = types.InlineKeyboardButton(
                "🔄 إعادة تعيين افتراضية",
                callback_data="admin_settings_reset_behavior"
            )
            btn_back = types.InlineKeyboardButton(
                "🔙 العودة للإعدادات",
                callback_data="admin_settings"
            )

            keyboard.add(btn_message_mode)
            keyboard.add(btn_typing_delay, btn_auto_delete)
            keyboard.add(btn_callback_notifications, btn_user_feedback)
            keyboard.add(btn_processing_messages)
            keyboard.add(btn_reset_behavior)
            keyboard.add(btn_back)

            response_delay = behavior_settings.get("response_delay", 0.5)
            max_message_length = behavior_settings.get("max_message_length", 4096)
            auto_delete_delay = behavior_settings.get("auto_delete_delay", 30)

            text = f"""
🎭 **إعدادات سلوك البوت**

⚙️ **الإعدادات الحالية:**

📝 **طريقة عرض الرسائل:** {message_mode}
• `edit` - تعديل الرسالة الحالية
• `new` - إرسال رسالة جديدة
• `both` - كلا الطريقتين حسب السياق

⏱️ **التوقيتات:**
• تأخير الكتابة: {typing_delay} ثانية
• تأخير الاستجابة: {response_delay} ثانية
• تأخير الحذف التلقائي: {auto_delete_delay} ثانية

🔔 **الإشعارات:**
• إشعارات الأزرار: {'مفعل' if callback_notifications else 'معطل'}
• رسائل المعالجة: {'مفعل' if processing_messages else 'معطل'}

💬 **التفاعل:**
• ملاحظات المستخدمين: {'مفعل' if user_feedback else 'معطل'}
• الحذف التلقائي: {'مفعل' if auto_delete else 'معطل'}
• الحد الأقصى لطول الرسالة: {max_message_length} حرف

🎯 **الوصف:**
• **طريقة العرض** - كيفية عرض الرسائل للمستخدمين
• **تأخير الكتابة** - مدة إظهار "يكتب..." قبل الرد
• **الحذف التلقائي** - حذف الرسائل تلقائياً بعد فترة
• **إشعارات الأزرار** - إظهار إشعارات عند الضغط على الأزرار
• **ملاحظات المستخدمين** - السماح للمستخدمين بإرسال ملاحظات

💡 **تلميح:** انقر على أي إعداد لتغييره
"""

            self.bot.edit_message_text(
                text,
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown',
                reply_markup=keyboard
            )

        except Exception as e:
            logger.error(f"❌ خطأ في إدارة إعدادات السلوك: {e}")
            self.bot.answer_callback_query(call.id, "❌ حدث خطأ")

    def handle_features_settings(self, call):
        """إدارة إعدادات المميزات"""
        try:
            keyboard = types.InlineKeyboardMarkup(row_width=2)

            # الحصول على إعدادات المميزات
            features_settings = bot_settings_manager.get_category_settings("features")

            # إنشاء أزرار للمميزات
            features = [
                ("enable_translation", "🌐 الترجمة"),
                ("enable_mcq_questions", "❓ أسئلة MCQ"),
                ("enable_file_upload", "📁 رفع الملفات"),
                ("enable_idea_suggestions", "💡 اقتراح الأفكار"),
                ("enable_services", "🛠️ الخدمات"),
                ("enable_educational_content", "📚 المحتوى التعليمي"),
                ("enable_ministerial_questions", "🗂️ الأسئلة الوزارية"),
                ("enable_user_progress_tracking", "📊 تتبع التقدم"),
                ("enable_bookmarks", "🔖 الإشارات المرجعية"),
                ("enable_search", "🔍 البحث")
            ]

            for feature_key, feature_name in features:
                is_enabled = features_settings.get(feature_key, True)
                status = "✅" if is_enabled else "❌"
                btn = types.InlineKeyboardButton(
                    f"{status} {feature_name}",
                    callback_data=f"admin_settings_toggle_feature_{feature_key}"
                )
                keyboard.add(btn)

            # أزرار إضافية
            btn_enable_all = types.InlineKeyboardButton(
                "✅ تفعيل الكل",
                callback_data="admin_settings_enable_all_features"
            )
            btn_disable_all = types.InlineKeyboardButton(
                "❌ إلغاء الكل",
                callback_data="admin_settings_disable_all_features"
            )
            btn_reset_features = types.InlineKeyboardButton(
                "🔄 إعادة تعيين افتراضية",
                callback_data="admin_settings_reset_features"
            )
            btn_back = types.InlineKeyboardButton(
                "🔙 العودة للإعدادات",
                callback_data="admin_settings"
            )

            keyboard.add(btn_enable_all, btn_disable_all)
            keyboard.add(btn_reset_features)
            keyboard.add(btn_back)

            # حساب الإحصائيات
            enabled_count = sum(1 for key, _ in features if features_settings.get(key, True))
            total_count = len(features)

            text = f"""
🎯 **إدارة مميزات البوت**

📊 **الإحصائيات:**
• المميزات المفعلة: {enabled_count}/{total_count}
• نسبة التفعيل: {(enabled_count/total_count)*100:.1f}%

🎛️ **المميزات المتاحة:**

🌐 **الترجمة** - ترجمة النصوص بين اللغات
❓ **أسئلة MCQ** - اختبارات متعددة الخيارات
📁 **رفع الملفات** - السماح برفع الملفات والوثائق
💡 **اقتراح الأفكار** - استقبال اقتراحات المستخدمين
🛠️ **الخدمات** - عرض وطلب الخدمات المتاحة
📚 **المحتوى التعليمي** - الوصول للمواد التعليمية
🗂️ **الأسئلة الوزارية** - أسئلة الامتحانات الوزارية
📊 **تتبع التقدم** - متابعة تقدم المستخدمين
🔖 **الإشارات المرجعية** - حفظ المحتوى المفضل
🔍 **البحث** - البحث في المحتوى

🎯 **التحكم السريع:**
• **تفعيل الكل** - تفعيل جميع المميزات
• **إلغاء الكل** - إلغاء جميع المميزات
• **إعادة تعيين** - العودة للإعدادات الافتراضية

💡 **تلميح:** انقر على أي ميزة لتفعيلها أو إلغائها
"""

            self.bot.edit_message_text(
                text,
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown',
                reply_markup=keyboard
            )

        except Exception as e:
            logger.error(f"❌ خطأ في إدارة إعدادات المميزات: {e}")
            self.bot.answer_callback_query(call.id, "❌ حدث خطأ")

    def handle_files_management(self, call):
        """إدارة الملفات والوسائط"""
        try:
            keyboard = types.InlineKeyboardMarkup(row_width=2)

            # الحصول على إحصائيات الملفات
            stats = file_manager.get_advanced_statistics()

            # أزرار إدارة الملفات
            btn_browse_files = types.InlineKeyboardButton(
                "📁 تصفح الملفات",
                callback_data="admin_files_browse"
            )
            btn_upload_file = types.InlineKeyboardButton(
                "📤 رفع ملف جديد",
                callback_data="admin_files_upload"
            )
            btn_create_folder = types.InlineKeyboardButton(
                "📂 إنشاء مجلد",
                callback_data="admin_files_create_folder"
            )
            btn_search_files = types.InlineKeyboardButton(
                "🔍 البحث في الملفات",
                callback_data="admin_files_search"
            )
            btn_categories = types.InlineKeyboardButton(
                "🗂️ إدارة الفئات",
                callback_data="admin_files_categories"
            )
            btn_compression = types.InlineKeyboardButton(
                "🗜️ ضغط الملفات",
                callback_data="admin_files_compression"
            )
            btn_thumbnails = types.InlineKeyboardButton(
                "🖼️ الصور المصغرة",
                callback_data="admin_files_thumbnails"
            )
            btn_cleanup = types.InlineKeyboardButton(
                "🧹 تنظيف الملفات القديمة",
                callback_data="admin_files_cleanup"
            )
            btn_statistics = types.InlineKeyboardButton(
                "📊 إحصائيات مفصلة",
                callback_data="admin_files_statistics"
            )
            btn_export = types.InlineKeyboardButton(
                "📤 تصدير قائمة الملفات",
                callback_data="admin_files_export"
            )
            btn_settings = types.InlineKeyboardButton(
                "⚙️ إعدادات الملفات",
                callback_data="admin_files_settings"
            )
            btn_back = types.InlineKeyboardButton(
                "🔙 العودة لإدارة المحتوى",
                callback_data="admin_content"
            )

            keyboard.add(btn_browse_files, btn_upload_file)
            keyboard.add(btn_create_folder, btn_search_files)
            keyboard.add(btn_categories, btn_compression)
            keyboard.add(btn_thumbnails, btn_cleanup)
            keyboard.add(btn_statistics, btn_export)
            keyboard.add(btn_settings)
            keyboard.add(btn_back)

            text = f"""
📁 **إدارة الملفات والوسائط المتقدمة**

📊 **الإحصائيات العامة:**
• إجمالي الملفات: {stats.get('total_files', 0)}
• إجمالي المجلدات: {stats.get('total_folders', 0)}
• الحجم الإجمالي: {stats.get('total_size_mb', 0)} ميجابايت
• الملفات المضغوطة: {stats.get('compressed_files', 0)}
• توفير الضغط: {stats.get('compression_saved_mb', 0)} ميجابايت
• الملفات مع صور مصغرة: {stats.get('files_with_thumbnails', 0)}

🎛️ **العمليات المتاحة:**

📁 **تصفح الملفات**
• عرض جميع الملفات والمجلدات
• التنقل بين المجلدات
• معاينة الملفات وتفاصيلها

📤 **رفع ملف جديد**
• رفع ملفات من الجهاز
• تحديد الفئة والوصف
• إضافة علامات للبحث

📂 **إنشاء مجلد**
• تنظيم الملفات في مجلدات
• إنشاء هيكل هرمي للملفات
• تحديد صلاحيات المجلدات

🔍 **البحث في الملفات**
• البحث بالاسم أو الوصف
• البحث بالعلامات
• تصفية حسب النوع والفئة

🗂️ **إدارة الفئات**
• إنشاء فئات جديدة للملفات
• تخصيص أيقونات الفئات
• تحديد قواعد كل فئة

🗜️ **ضغط الملفات**
• ضغط الملفات لتوفير المساحة
• عرض نسب الضغط
• إدارة الملفات المضغوطة

🖼️ **الصور المصغرة**
• إنشاء صور مصغرة للصور
• معاينة سريعة للملفات
• تحسين سرعة التحميل

🧹 **تنظيف الملفات القديمة**
• حذف الملفات غير المستخدمة
• تنظيف تلقائي دوري
• استعادة المساحة المحررة

📊 **إحصائيات مفصلة**
• تحليل استخدام المساحة
• إحصائيات حسب النوع والفئة
• تقارير الاستخدام

📤 **تصدير قائمة الملفات**
• تصدير معلومات الملفات
• إنشاء تقارير
• نسخ احتياطية للفهارس

⚙️ **إعدادات الملفات**
• تحديد حدود الحجم
• إعدادات الأمان
• تخصيص سلوك النظام

اختر العملية المطلوبة:
"""

            self.bot.edit_message_text(
                text,
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown',
                reply_markup=keyboard
            )

        except Exception as e:
            logger.error(f"❌ خطأ في إدارة الملفات: {e}")
            self.bot.answer_callback_query(call.id, "❌ حدث خطأ")

    def handle_backup_management(self, call):
        """إدارة النسخ الاحتياطي والاستيراد/التصدير"""
        try:
            keyboard = types.InlineKeyboardMarkup(row_width=2)

            # الحصول على قائمة النسخ الاحتياطية
            backup_list = backup_manager.get_backup_list()

            # أزرار إدارة النسخ الاحتياطي
            btn_create_full = types.InlineKeyboardButton(
                "💾 نسخة احتياطية كاملة",
                callback_data="admin_backup_create_full"
            )
            btn_create_selective = types.InlineKeyboardButton(
                "🎯 نسخة احتياطية انتقائية",
                callback_data="admin_backup_create_selective"
            )
            btn_restore = types.InlineKeyboardButton(
                "🔄 استعادة نسخة احتياطية",
                callback_data="admin_backup_restore"
            )
            btn_list_backups = types.InlineKeyboardButton(
                "📋 عرض النسخ الاحتياطية",
                callback_data="admin_backup_list"
            )
            btn_export_data = types.InlineKeyboardButton(
                "📤 تصدير البيانات",
                callback_data="admin_backup_export"
            )
            btn_import_data = types.InlineKeyboardButton(
                "📥 استيراد البيانات",
                callback_data="admin_backup_import"
            )
            btn_auto_backup = types.InlineKeyboardButton(
                "⏰ النسخ التلقائي",
                callback_data="admin_backup_auto_settings"
            )
            btn_cleanup = types.InlineKeyboardButton(
                "🧹 تنظيف النسخ القديمة",
                callback_data="admin_backup_cleanup"
            )
            btn_settings = types.InlineKeyboardButton(
                "⚙️ إعدادات النسخ الاحتياطي",
                callback_data="admin_backup_settings"
            )
            btn_back = types.InlineKeyboardButton(
                "🔙 العودة للوحة التحكم",
                callback_data="admin_back"
            )

            keyboard.add(btn_create_full, btn_create_selective)
            keyboard.add(btn_restore, btn_list_backups)
            keyboard.add(btn_export_data, btn_import_data)
            keyboard.add(btn_auto_backup, btn_cleanup)
            keyboard.add(btn_settings)
            keyboard.add(btn_back)

            # حساب إحصائيات النسخ الاحتياطية
            total_backups = len(backup_list)
            total_size = sum(
                os.path.getsize(backup["path"])
                for backup in backup_list
                if os.path.exists(backup["path"])
            ) if backup_list else 0
            total_size_mb = round(total_size / (1024 * 1024), 2)

            latest_backup = backup_list[0]["created_date"][:10] if backup_list else "لا يوجد"

            text = f"""
💾 **إدارة النسخ الاحتياطي والاستيراد/التصدير**

📊 **الإحصائيات العامة:**
• إجمالي النسخ الاحتياطية: {total_backups}
• الحجم الإجمالي: {total_size_mb} ميجابايت
• آخر نسخة احتياطية: {latest_backup}

🎛️ **العمليات المتاحة:**

💾 **إنشاء النسخ الاحتياطية:**
• **نسخة احتياطية كاملة** - نسخ جميع بيانات البوت
• **نسخة احتياطية انتقائية** - اختيار مكونات محددة للنسخ

🔄 **الاستعادة:**
• **استعادة نسخة احتياطية** - استعادة البيانات من نسخة محفوظة
• **عرض النسخ الاحتياطية** - تصفح وإدارة النسخ المحفوظة

📤📥 **الاستيراد والتصدير:**
• **تصدير البيانات** - تصدير بيانات محددة بصيغ مختلفة
• **استيراد البيانات** - استيراد بيانات من ملفات خارجية

⏰ **الأتمتة:**
• **النسخ التلقائي** - جدولة النسخ الاحتياطية التلقائية
• **تنظيف النسخ القديمة** - حذف النسخ القديمة تلقائياً

⚙️ **الإعدادات:**
• **إعدادات النسخ الاحتياطي** - تخصيص خيارات النسخ والحفظ

🎯 **المكونات المدعومة:**
• النصوص والرسائل
• إعدادات البوت
• الأزرار الديناميكية
• أسئلة MCQ
• المحتوى التعليمي
• بيانات الخدمات والمختصين
• معرفات المشرفين
• قوائم الملفات (اختياري)

💡 **مميزات متقدمة:**
• ضغط النسخ الاحتياطية لتوفير المساحة
• تشفير البيانات الحساسة (قريباً)
• النسخ التلقائي المجدول
• استعادة انتقائية للمكونات
• تصدير بصيغ متعددة (JSON, CSV)
• دمج البيانات المستوردة مع الموجودة

اختر العملية المطلوبة:
"""

            self.bot.edit_message_text(
                text,
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown',
                reply_markup=keyboard
            )

        except Exception as e:
            logger.error(f"❌ خطأ في إدارة النسخ الاحتياطي: {e}")
            self.bot.answer_callback_query(call.id, "❌ حدث خطأ")

    def handle_backup_create_full(self, call):
        """إنشاء نسخة احتياطية كاملة"""
        try:
            keyboard = types.InlineKeyboardMarkup(row_width=2)

            btn_with_files = types.InlineKeyboardButton(
                "📁 مع الملفات",
                callback_data="admin_backup_full_with_files"
            )
            btn_without_files = types.InlineKeyboardButton(
                "📄 بدون الملفات",
                callback_data="admin_backup_full_without_files"
            )
            btn_back = types.InlineKeyboardButton(
                "🔙 العودة لإدارة النسخ الاحتياطي",
                callback_data="admin_backup"
            )

            keyboard.add(btn_with_files, btn_without_files)
            keyboard.add(btn_back)

            text = """
💾 **إنشاء نسخة احتياطية كاملة**

اختر نوع النسخة الاحتياطية:

📁 **مع الملفات:**
• تشمل جميع بيانات البوت
• تشمل قوائم الملفات والوسائط
• حجم أكبر ووقت أطول
• مناسبة للنسخ الاحتياطية الشاملة

📄 **بدون الملفات:**
• تشمل جميع بيانات البوت
• لا تشمل الملفات والوسائط
• حجم أصغر ووقت أقل
• مناسبة للنسخ الاحتياطية السريعة

🎯 **المكونات المشمولة:**
• جميع النصوص والرسائل
• إعدادات البوت الكاملة
• الأزرار الديناميكية
• أسئلة MCQ وإعداداتها
• المحتوى التعليمي
• بيانات الخدمات والمختصين
• معرفات المشرفين

💡 **تلميح:** النسخة الاحتياطية ستكون مضغوطة لتوفير المساحة
"""

            self.bot.edit_message_text(
                text,
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown',
                reply_markup=keyboard
            )

        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء النسخة الاحتياطية الكاملة: {e}")
            self.bot.answer_callback_query(call.id, "❌ حدث خطأ")

    def handle_backup_create_selective(self, call):
        """إنشاء نسخة احتياطية انتقائية"""
        try:
            keyboard = types.InlineKeyboardMarkup(row_width=2)

            # أزرار اختيار المكونات
            components = [
                ("texts", "📝 النصوص"),
                ("settings", "⚙️ الإعدادات"),
                ("buttons", "🔘 الأزرار"),
                ("mcq", "❓ أسئلة MCQ"),
                ("content", "📚 المحتوى التعليمي"),
                ("services", "🛠️ الخدمات"),
                ("admins", "👨‍💼 المشرفين"),
                ("files", "📁 الملفات")
            ]

            for comp_id, comp_name in components:
                btn = types.InlineKeyboardButton(
                    f"☐ {comp_name}",
                    callback_data=f"admin_backup_toggle_{comp_id}"
                )
                keyboard.add(btn)

            btn_create = types.InlineKeyboardButton(
                "✅ إنشاء النسخة الاحتياطية",
                callback_data="admin_backup_selective_create"
            )
            btn_select_all = types.InlineKeyboardButton(
                "☑️ تحديد الكل",
                callback_data="admin_backup_select_all"
            )
            btn_deselect_all = types.InlineKeyboardButton(
                "☐ إلغاء تحديد الكل",
                callback_data="admin_backup_deselect_all"
            )
            btn_back = types.InlineKeyboardButton(
                "🔙 العودة لإدارة النسخ الاحتياطي",
                callback_data="admin_backup"
            )

            keyboard.add(btn_select_all, btn_deselect_all)
            keyboard.add(btn_create)
            keyboard.add(btn_back)

            text = """
🎯 **إنشاء نسخة احتياطية انتقائية**

اختر المكونات التي تريد تضمينها في النسخة الاحتياطية:

📝 **النصوص** - جميع نصوص ورسائل البوت
⚙️ **الإعدادات** - إعدادات البوت وسلوكه
🔘 **الأزرار** - تكوينات الأزرار الديناميكية
❓ **أسئلة MCQ** - أسئلة الاختبارات وإعداداتها
📚 **المحتوى التعليمي** - المراحل والمواد والدروس
🛠️ **الخدمات** - بيانات الخدمات والمختصين
👨‍💼 **المشرفين** - معرفات وصلاحيات المشرفين
📁 **الملفات** - قوائم الملفات والوسائط

💡 **تلميح:**
• انقر على أي مكون لتحديده أو إلغاء تحديده
• يمكنك تحديد مكونات متعددة
• استخدم "تحديد الكل" لتحديد جميع المكونات

⚠️ **ملاحظة:** تأكد من تحديد المكونات المطلوبة قبل إنشاء النسخة الاحتياطية
"""

            # حفظ حالة التحديد في البيانات المؤقتة
            if call.from_user.id not in self.temp_data:
                self.temp_data[call.from_user.id] = {}
            self.temp_data[call.from_user.id]["selected_components"] = []

            self.bot.edit_message_text(
                text,
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown',
                reply_markup=keyboard
            )

        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء النسخة الاحتياطية الانتقائية: {e}")
            self.bot.answer_callback_query(call.id, "❌ حدث خطأ")

    def handle_preview_management(self, call):
        """إدارة المعاينة والاختبار"""
        try:
            keyboard = types.InlineKeyboardMarkup(row_width=2)

            # أزرار إدارة المعاينة
            btn_preview_texts = types.InlineKeyboardButton(
                "📝 معاينة النصوص",
                callback_data="admin_preview_texts"
            )
            btn_preview_buttons = types.InlineKeyboardButton(
                "🔘 معاينة الأزرار",
                callback_data="admin_preview_buttons"
            )
            btn_preview_settings = types.InlineKeyboardButton(
                "⚙️ معاينة الإعدادات",
                callback_data="admin_preview_settings"
            )
            btn_test_environment = types.InlineKeyboardButton(
                "🧪 بيئة الاختبار",
                callback_data="admin_preview_test_env"
            )
            btn_preview_content = types.InlineKeyboardButton(
                "📚 معاينة المحتوى",
                callback_data="admin_preview_content"
            )
            btn_preview_services = types.InlineKeyboardButton(
                "🛠️ معاينة الخدمات",
                callback_data="admin_preview_services"
            )
            btn_live_preview = types.InlineKeyboardButton(
                "👁️ معاينة مباشرة",
                callback_data="admin_preview_live"
            )
            btn_test_scenarios = types.InlineKeyboardButton(
                "📋 سيناريوهات الاختبار",
                callback_data="admin_preview_scenarios"
            )
            btn_preview_sessions = types.InlineKeyboardButton(
                "📊 جلسات المعاينة",
                callback_data="admin_preview_sessions"
            )
            btn_cleanup = types.InlineKeyboardButton(
                "🧹 تنظيف الجلسات",
                callback_data="admin_preview_cleanup"
            )
            btn_back = types.InlineKeyboardButton(
                "🔙 العودة للوحة التحكم",
                callback_data="admin_back"
            )

            keyboard.add(btn_preview_texts, btn_preview_buttons)
            keyboard.add(btn_preview_settings, btn_test_environment)
            keyboard.add(btn_preview_content, btn_preview_services)
            keyboard.add(btn_live_preview, btn_test_scenarios)
            keyboard.add(btn_preview_sessions, btn_cleanup)
            keyboard.add(btn_back)

            # حساب إحصائيات المعاينة
            active_sessions = len([s for s in self.preview_manager.preview_sessions.values() if s.get("status") == "active"])
            total_sessions = len(self.preview_manager.preview_sessions)

            text = f"""
👁️ **إدارة المعاينة والاختبار**

📊 **الإحصائيات:**
• الجلسات النشطة: {active_sessions}
• إجمالي الجلسات: {total_sessions}

🎛️ **أنواع المعاينة المتاحة:**

📝 **معاينة النصوص**
• معاينة تغييرات النصوص قبل الحفظ
• اختبار المتغيرات الديناميكية
• مقارنة النصوص القديمة والجديدة

🔘 **معاينة الأزرار**
• معاينة تكوينات الأزرار الجديدة
• اختبار التخطيط والشكل
• اختبار الإجراءات والروابط

⚙️ **معاينة الإعدادات**
• معاينة تغييرات الإعدادات
• اختبار تأثير الإعدادات على السلوك
• مقارنة الإعدادات القديمة والجديدة

🧪 **بيئة الاختبار**
• بيئة آمنة لاختبار التغييرات
• محاكاة تفاعل المستخدمين
• اختبار السيناريوهات المختلفة

📚 **معاينة المحتوى**
• معاينة المحتوى التعليمي الجديد
• اختبار التنقل بين الأقسام
• معاينة الهيكل الهرمي

🛠️ **معاينة الخدمات**
• معاينة الخدمات والمختصين الجدد
• اختبار عمليات الطلب والتواصل
• معاينة واجهات الخدمات

👁️ **المعاينة المباشرة**
• معاينة فورية للتغييرات
• اختبار تفاعلي مع البوت
• محاكاة تجربة المستخدم الحقيقية

📋 **سيناريوهات الاختبار**
• سيناريوهات اختبار محددة مسبقاً
• اختبار حالات الاستخدام المختلفة
• اختبار الأخطاء والاستثناءات

📊 **إدارة الجلسات**
• عرض جلسات المعاينة النشطة
• تتبع تفاعلات المستخدمين
• تنظيف الجلسات القديمة

🎯 **المميزات:**
• معاينة آمنة بدون تأثير على النظام الحقيقي
• اختبار شامل لجميع المكونات
• إمكانية التراجع عن التغييرات
• تسجيل مفصل لجميع العمليات
• واجهات سهلة الاستخدام

💡 **تلميح:** استخدم المعاينة دائماً قبل تطبيق أي تغييرات مهمة

اختر نوع المعاينة المطلوب:
"""

            self.bot.edit_message_text(
                text,
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown',
                reply_markup=keyboard
            )

        except Exception as e:
            logger.error(f"❌ خطأ في إدارة المعاينة: {e}")
            self.bot.answer_callback_query(call.id, "❌ حدث خطأ")

    def handle_permissions_management(self, call):
        """إدارة الصلاحيات والأدوار المتقدمة"""
        try:
            keyboard = types.InlineKeyboardMarkup(row_width=2)

            # الحصول على ملخص الصلاحيات
            permissions_summary = permissions_manager.get_permissions_summary()

            # أزرار إدارة الصلاحيات
            btn_manage_roles = types.InlineKeyboardButton(
                "👥 إدارة الأدوار",
                callback_data="admin_permissions_roles"
            )
            btn_manage_users = types.InlineKeyboardButton(
                "👤 إدارة صلاحيات المستخدمين",
                callback_data="admin_permissions_users"
            )
            btn_create_role = types.InlineKeyboardButton(
                "➕ إنشاء دور جديد",
                callback_data="admin_permissions_create_role"
            )
            btn_assign_permissions = types.InlineKeyboardButton(
                "🔐 تعيين صلاحيات",
                callback_data="admin_permissions_assign"
            )
            btn_view_permissions = types.InlineKeyboardButton(
                "📋 عرض الصلاحيات",
                callback_data="admin_permissions_view"
            )
            btn_audit_log = types.InlineKeyboardButton(
                "📊 سجل الصلاحيات",
                callback_data="admin_permissions_audit"
            )
            btn_permission_templates = types.InlineKeyboardButton(
                "📝 قوالب الصلاحيات",
                callback_data="admin_permissions_templates"
            )
            btn_bulk_operations = types.InlineKeyboardButton(
                "⚡ عمليات جماعية",
                callback_data="admin_permissions_bulk"
            )
            btn_settings = types.InlineKeyboardButton(
                "⚙️ إعدادات الصلاحيات",
                callback_data="admin_permissions_settings"
            )
            btn_back = types.InlineKeyboardButton(
                "🔙 العودة للوحة التحكم",
                callback_data="admin_back"
            )

            keyboard.add(btn_manage_roles, btn_manage_users)
            keyboard.add(btn_create_role, btn_assign_permissions)
            keyboard.add(btn_view_permissions, btn_audit_log)
            keyboard.add(btn_permission_templates, btn_bulk_operations)
            keyboard.add(btn_settings)
            keyboard.add(btn_back)

            text = f"""
🔐 **إدارة الصلاحيات والأدوار المتقدمة**

📊 **الإحصائيات العامة:**
• إجمالي الأدوار: {permissions_summary.get('total_roles', 0)}
• الأدوار النشطة: {permissions_summary.get('active_roles', 0)}
• المستخدمين مع صلاحيات: {permissions_summary.get('total_users_with_permissions', 0)}
• المستخدمين النشطين: {permissions_summary.get('active_users_with_permissions', 0)}
• فئات الصلاحيات: {permissions_summary.get('permission_categories', 0)}
• إجمالي الصلاحيات: {permissions_summary.get('total_permissions', 0)}

🎛️ **العمليات المتاحة:**

👥 **إدارة الأدوار**
• عرض وتحرير الأدوار الموجودة
• تحديد مستويات الصلاحيات
• إدارة قيود الأدوار
• تفعيل/إلغاء الأدوار

👤 **إدارة صلاحيات المستخدمين**
• عرض صلاحيات المستخدمين
• تعيين أدوار للمستخدمين
• إضافة صلاحيات مخصصة
• إدارة القيود الفردية

➕ **إنشاء دور جديد**
• تصميم أدوار مخصصة
• تحديد فئات الصلاحيات
• تعيين مستوى الدور
• إضافة قيود محددة

🔐 **تعيين صلاحيات**
• تعيين أدوار للمستخدمين الجدد
• تحديث صلاحيات المستخدمين الحاليين
• إدارة الصلاحيات المؤقتة
• تعيين صلاحيات طوارئ

📋 **عرض الصلاحيات**
• عرض هيكل الصلاحيات الكامل
• مقارنة الأدوار المختلفة
• عرض صلاحيات مستخدم محدد
• تحليل توزيع الصلاحيات

📊 **سجل الصلاحيات**
• تتبع تغييرات الصلاحيات
• سجل تعيين/إزالة الأدوار
• مراجعة العمليات الحساسة
• تحليل أنماط الاستخدام

📝 **قوالب الصلاحيات**
• قوالب جاهزة للأدوار الشائعة
• إنشاء قوالب مخصصة
• استيراد/تصدير القوالب
• مشاركة التكوينات

⚡ **عمليات جماعية**
• تعيين أدوار لمجموعات
• تحديث صلاحيات متعددة
• نقل صلاحيات بين المستخدمين
• عمليات التنظيف الجماعي

⚙️ **إعدادات الصلاحيات**
• تخصيص سلوك النظام
• إعدادات الأمان
• قواعد التحقق التلقائي
• إعدادات التنبيهات

🎯 **الأدوار الافتراضية:**
• **مشرف عام** - صلاحيات كاملة (مستوى 10)
• **مشرف المحتوى** - إدارة المحتوى (مستوى 7)
• **مشرف الخدمات** - إدارة الخدمات (مستوى 6)
• **مشرف** - إشراف عام (مستوى 5)
• **دعم فني** - دعم محدود (مستوى 4)
• **محلل** - عرض التحليلات (مستوى 3)

🔒 **مميزات الأمان:**
• نظام مستويات متدرج
• قيود مخصصة لكل دور
• تتبع جميع العمليات
• منع تصعيد الصلاحيات غير المصرح
• نظام موافقات للعمليات الحساسة

💡 **تلميح:** استخدم نظام المستويات لضمان عدم تمكن المشرفين من إدارة مستخدمين بمستوى أعلى منهم

اختر العملية المطلوبة:
"""

            self.bot.edit_message_text(
                text,
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown',
                reply_markup=keyboard
            )

        except Exception as e:
            logger.error(f"❌ خطأ في إدارة الصلاحيات: {e}")
            self.bot.answer_callback_query(call.id, "❌ حدث خطأ")

# دالة مساعدة لإنشاء مثيل لوحة التحكم
def create_admin_panel(bot):
    """إنشاء مثيل لوحة التحكم"""
    return AdminPanel(bot)
