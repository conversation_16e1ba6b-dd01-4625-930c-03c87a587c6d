#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام الأزرار الديناميكي للبوت التعليمي
Dynamic Button System for Educational Bot

يدير إنشاء وتعديل الأزرار بشكل ديناميكي مع أزرار + للتنسيق السهل
"""

import logging
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime
from telebot import types
from firebase_manager import firebase_manager

logger = logging.getLogger(__name__)

class DynamicButtonSystem:
    """نظام الأزرار الديناميكي"""
    
    def __init__(self, bot):
        self.bot = bot
        self.button_sessions = {}  # جلسات تحرير الأزرار
        self.button_types = {
            "keyboard": "أزرار لوحة المفاتيح",
            "inline": "أزرار مضمنة"
        }
        self.action_types = {
            "section": "انتقال لقسم",
            "content": "عرض محتوى",
            "url": "رابط خارجي",
            "callback": "إجراء مخصص"
        }
    
    def show_admin_button_panel(self, chat_id: int, message_id: int = None, section_key: str = None):
        """عرض لوحة تحكم الأزرار للمشرف"""
        try:
            # تحميل الأزرار الحالية للقسم
            current_buttons = self._get_section_buttons(section_key) if section_key else []
            
            keyboard = types.InlineKeyboardMarkup(row_width=1)
            
            admin_text = f"""
🎛️ **إدارة الأزرار - {section_key or 'القائمة الرئيسية'}**

📝 **وضع التعديل النشط**

"""
            
            if not current_buttons or len(current_buttons) == 0:
                # لا توجد أزرار - عرض زر الإضافة الأول
                admin_text += """
🔍 **لا توجد أزرار مخصصة حالياً**

💡 **يمكنك إضافة أزرار جديدة:**
"""
                
                # زر إضافة الزر الأول
                btn_add_first = types.InlineKeyboardButton(
                    "➕ | 0 - إضافة زر جديد",
                    callback_data=f"admin_add_button_{section_key or 'main'}_0"
                )
                keyboard.add(btn_add_first)
                
            else:
                # يوجد أزرار - عرضها مع أزرار التعديل والإضافة
                admin_text += "🎯 **الأزرار الحالية:**\n\n"
                
                for i, button_info in enumerate(current_buttons):
                    button_text = button_info.get('text', f'زر {i+1}')
                    button_type = button_info.get('type', 'keyboard')
                    
                    # عرض معلومات الزر
                    admin_text += f"🔘 **{i+1}.** {button_text} ({self.button_types.get(button_type, button_type)})\n"
                    
                    # إنشاء صف أزرار للتحكم في هذا الزر
                    row_buttons = []
                    
                    # زر تعديل
                    btn_edit = types.InlineKeyboardButton(
                        f"✏️ تعديل",
                        callback_data=f"admin_edit_button_{section_key or 'main'}_{i}"
                    )
                    row_buttons.append(btn_edit)
                    
                    # زر حذف
                    btn_delete = types.InlineKeyboardButton(
                        f"🗑️ حذف",
                        callback_data=f"admin_delete_button_{section_key or 'main'}_{i}"
                    )
                    row_buttons.append(btn_delete)
                    
                    keyboard.row(*row_buttons)
                    
                    # زر إضافة بعد هذا الزر
                    btn_add_after = types.InlineKeyboardButton(
                        f"➕ | {i+1} - إضافة بعد {button_text[:15]}...",
                        callback_data=f"admin_add_button_{section_key or 'main'}_{i+1}"
                    )
                    keyboard.add(btn_add_after)
                
                admin_text += "\n"
                
                # زر إضافة في البداية
                btn_add_first = types.InlineKeyboardButton(
                    "➕ | 0 - إضافة في البداية",
                    callback_data=f"admin_add_button_{section_key or 'main'}_0"
                )
                keyboard.add(btn_add_first)
            
            # أزرار إضافية
            keyboard.add(types.InlineKeyboardButton(
                "👁️ معاينة الأزرار",
                callback_data=f"admin_preview_buttons_{section_key or 'main'}"
            ))
            
            keyboard.add(types.InlineKeyboardButton(
                "💾 حفظ التغييرات",
                callback_data=f"admin_save_buttons_{section_key or 'main'}"
            ))
            
            # زر العودة
            btn_back = types.InlineKeyboardButton(
                "🔙 العودة",
                callback_data="admin_content"
            )
            keyboard.add(btn_back)
            
            if message_id:
                self.bot.edit_message_text(
                    admin_text,
                    chat_id,
                    message_id,
                    parse_mode='Markdown',
                    reply_markup=keyboard
                )
            else:
                self.bot.send_message(
                    chat_id,
                    admin_text,
                    parse_mode='Markdown',
                    reply_markup=keyboard
                )
                
        except Exception as e:
            logger.error(f"❌ خطأ في عرض لوحة تحكم الأزرار: {e}")
    
    def handle_add_button(self, call, section_key: str, position: int):
        """معالجة إضافة زر جديد"""
        try:
            user_id = call.from_user.id
            
            # إنشاء جلسة تحرير زر
            session_id = f"btn_{user_id}_{section_key}_{position}_{datetime.now().timestamp()}"
            self.button_sessions[session_id] = {
                "user_id": user_id,
                "section_key": section_key,
                "position": position,
                "action": "add_button",
                "step": "type",
                "data": {}
            }
            
            # اختيار نوع الزر
            keyboard = types.InlineKeyboardMarkup(row_width=2)
            
            btn_keyboard = types.InlineKeyboardButton(
                "⌨️ زر لوحة مفاتيح",
                callback_data=f"admin_button_type_{session_id}_keyboard"
            )
            btn_inline = types.InlineKeyboardButton(
                "🔘 زر مضمن",
                callback_data=f"admin_button_type_{session_id}_inline"
            )
            
            keyboard.add(btn_keyboard, btn_inline)
            
            btn_cancel = types.InlineKeyboardButton(
                "❌ إلغاء",
                callback_data=f"admin_cancel_button_session_{session_id}"
            )
            keyboard.add(btn_cancel)
            
            self.bot.edit_message_text(
                f"""
➕ **إضافة زر جديد**

📍 **الموضع:** {position}
📂 **القسم:** {section_key}

🎯 **الخطوة 1: نوع الزر**

اختر نوع الزر الذي تريد إضافته:

⌨️ **زر لوحة مفاتيح:** يظهر في لوحة المفاتيح أسفل الشاشة
🔘 **زر مضمن:** يظهر مضمناً في الرسالة
""",
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown',
                reply_markup=keyboard
            )
            
            self.bot.answer_callback_query(call.id, "✅ اختر نوع الزر")
            
        except Exception as e:
            logger.error(f"❌ خطأ في معالجة إضافة زر: {e}")
            self.bot.answer_callback_query(call.id, "❌ حدث خطأ")
    
    def handle_button_type_selection(self, call, session_id: str, button_type: str):
        """معالجة اختيار نوع الزر"""
        try:
            session = self.button_sessions.get(session_id)
            if not session:
                self.bot.answer_callback_query(call.id, "❌ جلسة منتهية الصلاحية")
                return
            
            # حفظ نوع الزر والانتقال للخطوة التالية
            session["data"]["type"] = button_type
            session["step"] = "text"
            
            # طلب نص الزر
            keyboard = types.InlineKeyboardMarkup()
            btn_cancel = types.InlineKeyboardButton(
                "❌ إلغاء",
                callback_data=f"admin_cancel_button_session_{session_id}"
            )
            keyboard.add(btn_cancel)
            
            self.bot.edit_message_text(
                f"""
➕ **إضافة زر جديد**

✅ **نوع الزر:** {self.button_types.get(button_type, button_type)}

📝 **الخطوة 2: نص الزر**

أرسل النص الذي تريد أن يظهر على الزر:

💡 **أمثلة:**
• "📚 الكتب والمراجع"
• "🎯 اختبارات تفاعلية"
• "📞 تواصل معنا"

⚠️ **ملاحظة:** يفضل استخدام الرموز التعبيرية لجعل الزر أكثر جاذبية
""",
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown',
                reply_markup=keyboard
            )
            
            self.bot.answer_callback_query(call.id, f"✅ تم اختيار {self.button_types.get(button_type)}")
            
        except Exception as e:
            logger.error(f"❌ خطأ في معالجة اختيار نوع الزر: {e}")
            self.bot.answer_callback_query(call.id, "❌ حدث خطأ")
    
    def _get_section_buttons(self, section_key: str) -> List[Dict[str, Any]]:
        """الحصول على أزرار قسم معين"""
        try:
            # تحميل تكوين الأزرار من Firebase
            buttons_config = firebase_manager.get_dynamic_buttons() or {}
            section_config = buttons_config.get(section_key, {})
            return section_config.get("buttons", [])
        except Exception as e:
            logger.error(f"❌ خطأ في تحميل أزرار القسم {section_key}: {e}")
            return []
    
    def _save_section_buttons(self, section_key: str, buttons: List[Dict[str, Any]]) -> bool:
        """حفظ أزرار قسم معين"""
        try:
            # تحميل التكوين الحالي
            buttons_config = firebase_manager.get_dynamic_buttons() or {}
            
            # تحديث أزرار القسم
            if section_key not in buttons_config:
                buttons_config[section_key] = {}
            
            buttons_config[section_key]["buttons"] = buttons
            buttons_config[section_key]["last_updated"] = datetime.now().isoformat()
            
            # حفظ التكوين المحدث
            return firebase_manager.save_dynamic_buttons(buttons_config)
            
        except Exception as e:
            logger.error(f"❌ خطأ في حفظ أزرار القسم {section_key}: {e}")
            return False

# إنشاء مثيل عام
dynamic_button_system = None

def create_dynamic_button_system(bot):
    """إنشاء نظام الأزرار الديناميكي"""
    global dynamic_button_system
    dynamic_button_system = DynamicButtonSystem(bot)
    return dynamic_button_system
