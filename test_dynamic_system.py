#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار النظام الديناميكي الجديد للبوت التعليمي
Test Dynamic System for Educational Bot

اختبار الوظائف الجديدة للإدارة الديناميكية
"""

import os
import sys
import logging
from datetime import datetime

# إضافة المجلد الحالي للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# إعداد نظام التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_dynamic.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def test_dynamic_content_manager():
    """اختبار مدير المحتوى الديناميكي"""
    try:
        from dynamic_content_manager import DynamicContentManager
        
        # إنشاء مثيل وهمي للبوت
        class MockBot:
            def send_message(self, chat_id, text, **kwargs):
                print(f"Bot Message to {chat_id}: {text}")
            
            def edit_message_text(self, text, chat_id, message_id, **kwargs):
                print(f"Bot Edit Message {message_id} to {chat_id}: {text}")
        
        mock_bot = MockBot()
        manager = DynamicContentManager(mock_bot)
        
        print("✅ تم إنشاء مدير المحتوى الديناميكي بنجاح")
        print(f"📋 الأقسام المتاحة: {list(manager.content_structure.keys())}")
        
        # اختبار تحميل المحتوى
        for section_key in manager.content_structure.keys():
            content = manager._get_section_content(section_key)
            print(f"📂 {section_key}: {len(content) if content else 0} عنصر")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار مدير المحتوى الديناميكي: {e}")
        return False

def test_dynamic_button_system():
    """اختبار نظام الأزرار الديناميكي"""
    try:
        from dynamic_button_system import DynamicButtonSystem
        
        # إنشاء مثيل وهمي للبوت
        class MockBot:
            def send_message(self, chat_id, text, **kwargs):
                print(f"Bot Message to {chat_id}: {text}")
            
            def edit_message_text(self, text, chat_id, message_id, **kwargs):
                print(f"Bot Edit Message {message_id} to {chat_id}: {text}")
            
            def answer_callback_query(self, call_id, text):
                print(f"Bot Callback Answer {call_id}: {text}")
        
        mock_bot = MockBot()
        system = DynamicButtonSystem(mock_bot)
        
        print("✅ تم إنشاء نظام الأزرار الديناميكي بنجاح")
        print(f"🎛️ أنواع الأزرار: {list(system.button_types.keys())}")
        print(f"⚙️ أنواع الإجراءات: {list(system.action_types.keys())}")
        
        # اختبار تحميل الأزرار
        buttons = system._get_section_buttons("main")
        print(f"🔘 أزرار القائمة الرئيسية: {len(buttons)} زر")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار نظام الأزرار الديناميكي: {e}")
        return False

def test_firebase_connection():
    """اختبار الاتصال بـ Firebase"""
    try:
        from firebase_manager import firebase_manager
        
        # اختبار الاتصال
        print("🔄 اختبار الاتصال بـ Firebase...")
        
        # محاولة قراءة البيانات
        educational_content = firebase_manager.get_educational_content()
        ministerial_questions = firebase_manager.get_ministerial_questions()
        services_data = firebase_manager.get_services_data()
        
        print(f"📚 المحتوى التعليمي: {len(educational_content) if educational_content else 0} مرحلة")
        print(f"🗂️ المواد الوزارية: {len(ministerial_questions) if ministerial_questions else 0} مادة")
        print(f"🛠️ الخدمات: {len(services_data) if services_data else 0} خدمة")
        
        print("✅ تم الاتصال بـ Firebase بنجاح")
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في الاتصال بـ Firebase: {e}")
        return False

def test_bot_imports():
    """اختبار استيراد ملفات البوت"""
    try:
        print("🔄 اختبار استيراد الملفات...")
        
        # اختبار الاستيرادات الأساسية
        from firebase_manager import firebase_manager
        print("✅ firebase_manager")
        
        from text_manager import text_manager
        print("✅ text_manager")
        
        from button_manager import button_manager
        print("✅ button_manager")
        
        from dynamic_content_manager import create_dynamic_content_manager
        print("✅ dynamic_content_manager")
        
        from dynamic_button_system import create_dynamic_button_system
        print("✅ dynamic_button_system")
        
        print("✅ تم استيراد جميع الملفات بنجاح")
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في استيراد الملفات: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار النظام الديناميكي الجديد")
    print("=" * 50)
    
    tests = [
        ("استيراد الملفات", test_bot_imports),
        ("الاتصال بـ Firebase", test_firebase_connection),
        ("مدير المحتوى الديناميكي", test_dynamic_content_manager),
        ("نظام الأزرار الديناميكي", test_dynamic_button_system),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 اختبار: {test_name}")
        print("-" * 30)
        
        try:
            result = test_func()
            results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
                
        except Exception as e:
            logger.error(f"❌ خطأ في اختبار {test_name}: {e}")
            results.append((test_name, False))
            print(f"❌ {test_name}: خطأ - {e}")
    
    # عرض النتائج النهائية
    print("\n" + "=" * 50)
    print("📊 نتائج الاختبارات:")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 النتيجة النهائية: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام")
    else:
        print("⚠️ بعض الاختبارات فشلت، يرجى مراجعة الأخطاء")
    
    print(f"📅 تاريخ الاختبار: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
