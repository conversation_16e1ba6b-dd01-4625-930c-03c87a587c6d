#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير المحتوى الديناميكي للبوت التعليمي
Dynamic Content Manager for Educational Bot

يدير إنشاء وتعديل المحتوى بشكل ديناميكي مع واجهة تحكم تحاكي الواجهة العادية
"""

import logging
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime
from telebot import types
from firebase_manager import firebase_manager

logger = logging.getLogger(__name__)

class DynamicContentManager:
    """مدير المحتوى الديناميكي"""
    
    def __init__(self, bot):
        self.bot = bot
        self.admin_sessions = {}  # جلسات التحرير للمشرفين
        self.content_structure = {
            "educational_content": {
                "name": "📖 المحتوى العلمي",
                "type": "hierarchical",
                "levels": ["stage", "subject", "chapter"],
                "empty_message": "لا يوجد محتوى تعليمي متاح حالياً"
            },
            "ministerial_materials": {
                "name": "🗂️ المواد الوزارية", 
                "type": "flat",
                "empty_message": "لا توجد مواد وزارية متاحة حالياً"
            },
            "services": {
                "name": "🛠️ الخدمات",
                "type": "flat", 
                "empty_message": "لا توجد خدمات متاحة حالياً"
            }
        }
    
    def show_admin_content_panel(self, chat_id: int, message_id: int = None, user_id: int = None):
        """عرض لوحة التحكم في المحتوى للمشرف"""
        try:
            # إنشاء كيبورد يحاكي الواجهة العادية مع إضافة "وضع التعديل"
            keyboard = types.InlineKeyboardMarkup(row_width=1)
            
            # عنوان يوضح أنه في وضع التعديل
            admin_text = """
🔧 **لوحة التحكم - وضع التعديل**

مرحباً بك في وضع التعديل! 

📝 **الأقسام المتاحة للتعديل:**

اختر القسم الذي تريد تعديله أو إضافة محتوى إليه:
"""
            
            # إضافة أزرار الأقسام مع إشارة وضع التعديل
            for section_key, section_info in self.content_structure.items():
                btn_text = f"✏️ {section_info['name']} (تعديل)"
                btn = types.InlineKeyboardButton(
                    btn_text,
                    callback_data=f"admin_edit_section_{section_key}"
                )
                keyboard.add(btn)
            
            # زر العودة للوحة التحكم الرئيسية
            btn_back = types.InlineKeyboardButton(
                "🔙 العودة للوحة التحكم",
                callback_data="admin_back"
            )
            keyboard.add(btn_back)
            
            if message_id:
                self.bot.edit_message_text(
                    admin_text,
                    chat_id,
                    message_id,
                    parse_mode='Markdown',
                    reply_markup=keyboard
                )
            else:
                self.bot.send_message(
                    chat_id,
                    admin_text,
                    parse_mode='Markdown',
                    reply_markup=keyboard
                )
                
        except Exception as e:
            logger.error(f"❌ خطأ في عرض لوحة التحكم في المحتوى: {e}")
    
    def show_section_edit_panel(self, call, section_key: str):
        """عرض لوحة تعديل قسم معين"""
        try:
            section_info = self.content_structure.get(section_key)
            if not section_info:
                self.bot.answer_callback_query(call.id, "❌ قسم غير موجود")
                return
            
            # تحميل المحتوى الحالي من قاعدة البيانات
            current_content = self._get_section_content(section_key)
            
            # إنشاء واجهة تحاكي الواجهة العادية مع أزرار التعديل
            keyboard = types.InlineKeyboardMarkup(row_width=1)
            
            admin_text = f"""
✏️ **تعديل {section_info['name']}**

🎛️ **وضع التعديل النشط**

"""
            
            if not current_content or len(current_content) == 0:
                # لا يوجد محتوى - عرض رسالة فارغة مع زر الإضافة
                admin_text += f"""
🔍 **{section_info['empty_message']}**

💡 **يمكنك إضافة محتوى جديد باستخدام الأزرار أدناه:**
"""
                
                # زر إضافة العنصر الأول
                btn_add_first = types.InlineKeyboardButton(
                    "➕ | 0 - إضافة عنصر جديد",
                    callback_data=f"admin_add_item_{section_key}_0"
                )
                keyboard.add(btn_add_first)
                
            else:
                # يوجد محتوى - عرض العناصر مع أزرار التعديل والإضافة
                admin_text += "📋 **العناصر الحالية:**\n\n"
                
                items = list(current_content.keys()) if isinstance(current_content, dict) else current_content
                
                for i, item_name in enumerate(items):
                    # عرض العنصر الحالي
                    admin_text += f"📌 **{i+1}.** {item_name}\n"
                    
                    # زر تعديل العنصر
                    btn_edit = types.InlineKeyboardButton(
                        f"✏️ تعديل: {item_name}",
                        callback_data=f"admin_edit_item_{section_key}_{i}_{item_name}"
                    )
                    keyboard.add(btn_edit)
                    
                    # زر إضافة عنصر جديد بعد هذا العنصر
                    btn_add_after = types.InlineKeyboardButton(
                        f"➕ | {i+1} - إضافة بعد {item_name}",
                        callback_data=f"admin_add_item_{section_key}_{i+1}"
                    )
                    keyboard.add(btn_add_after)
                
                admin_text += "\n"
                
                # زر إضافة في البداية
                btn_add_first = types.InlineKeyboardButton(
                    "➕ | 0 - إضافة في البداية",
                    callback_data=f"admin_add_item_{section_key}_0"
                )
                keyboard.add(btn_add_first)
            
            # زر العودة
            btn_back = types.InlineKeyboardButton(
                "🔙 العودة لقائمة الأقسام",
                callback_data="admin_content"
            )
            keyboard.add(btn_back)
            
            self.bot.edit_message_text(
                admin_text,
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown',
                reply_markup=keyboard
            )
            
        except Exception as e:
            logger.error(f"❌ خطأ في عرض لوحة تعديل القسم: {e}")
            self.bot.answer_callback_query(call.id, "❌ حدث خطأ")
    
    def handle_add_item(self, call, section_key: str, position: int):
        """معالجة إضافة عنصر جديد"""
        try:
            user_id = call.from_user.id
            
            # إنشاء جلسة تحرير
            session_id = f"{user_id}_{section_key}_{position}_{datetime.now().timestamp()}"
            self.admin_sessions[session_id] = {
                "user_id": user_id,
                "section_key": section_key,
                "position": position,
                "action": "add_item",
                "step": "name",
                "data": {}
            }
            
            # طلب اسم العنصر الجديد
            keyboard = types.InlineKeyboardMarkup()
            btn_cancel = types.InlineKeyboardButton(
                "❌ إلغاء",
                callback_data=f"admin_cancel_session_{session_id}"
            )
            keyboard.add(btn_cancel)
            
            self.bot.edit_message_text(
                f"""
➕ **إضافة عنصر جديد**

📝 **الخطوة 1: اسم العنصر**

أرسل اسم العنصر الجديد الذي تريد إضافته:

💡 **مثال:** "الفيزياء الطبية" أو "خدمة التصميم"
""",
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown',
                reply_markup=keyboard
            )
            
            self.bot.answer_callback_query(call.id, "✅ أرسل اسم العنصر الجديد")
            
        except Exception as e:
            logger.error(f"❌ خطأ في معالجة إضافة عنصر: {e}")
            self.bot.answer_callback_query(call.id, "❌ حدث خطأ")
    
    def _get_section_content(self, section_key: str) -> Dict[str, Any]:
        """الحصول على محتوى قسم معين من قاعدة البيانات"""
        try:
            if section_key == "educational_content":
                return firebase_manager.get_educational_content() or {}
            elif section_key == "ministerial_materials":
                return firebase_manager.get_ministerial_questions() or {}
            elif section_key == "services":
                return firebase_manager.get_services_data() or {}
            else:
                return {}
        except Exception as e:
            logger.error(f"❌ خطأ في تحميل محتوى القسم {section_key}: {e}")
            return {}
    
    def _save_section_content(self, section_key: str, content: Dict[str, Any]) -> bool:
        """حفظ محتوى قسم معين في قاعدة البيانات"""
        try:
            if section_key == "educational_content":
                return firebase_manager.save_educational_content(content)
            elif section_key == "ministerial_materials":
                return firebase_manager.save_ministerial_questions(content)
            elif section_key == "services":
                return firebase_manager.save_services_data(content)
            else:
                return False
        except Exception as e:
            logger.error(f"❌ خطأ في حفظ محتوى القسم {section_key}: {e}")
            return False

# إنشاء مثيل عام
dynamic_content_manager = None

def create_dynamic_content_manager(bot):
    """إنشاء مدير المحتوى الديناميكي"""
    global dynamic_content_manager
    dynamic_content_manager = DynamicContentManager(bot)
    return dynamic_content_manager
