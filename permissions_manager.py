#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير الصلاحيات المتدرج للبوت التعليمي
Advanced Permissions Manager for Educational Bot

يدير نظام صلاحيات متعدد المستويات للمشرفين والمستخدمين
"""

import logging
from typing import Dict, Any, Optional, List, Set
from datetime import datetime
from firebase_manager import firebase_manager

logger = logging.getLogger(__name__)

class PermissionsManager:
    """مدير الصلاحيات المتدرج"""
    
    def __init__(self):
        self.permissions_data = {}
        self.roles_data = {}
        self.user_permissions = {}
        self.load_permissions_from_firebase()
    
    def load_permissions_from_firebase(self):
        """تحميل بيانات الصلاحيات من Firebase"""
        try:
            self.permissions_data = firebase_manager.get_permissions_data() or self.get_default_permissions()
            self.roles_data = firebase_manager.get_roles_data() or self.get_default_roles()
            self.user_permissions = firebase_manager.get_user_permissions() or {}
            logger.info("✅ تم تحميل بيانات الصلاحيات من Firebase")
        except Exception as e:
            logger.error(f"❌ خطأ في تحميل بيانات الصلاحيات: {e}")
            self.permissions_data = self.get_default_permissions()
            self.roles_data = self.get_default_roles()
            self.user_permissions = {}
    
    def get_default_permissions(self) -> Dict[str, Any]:
        """الصلاحيات الافتراضية"""
        return {
            "admin": {
                "name": "إدارة عامة",
                "description": "صلاحيات الإدارة العامة للبوت",
                "permissions": [
                    "view_admin_panel",
                    "manage_users",
                    "manage_admins",
                    "view_statistics",
                    "send_broadcast",
                    "manage_backup",
                    "manage_settings",
                    "access_logs"
                ]
            },
            "content": {
                "name": "إدارة المحتوى",
                "description": "صلاحيات إدارة المحتوى التعليمي",
                "permissions": [
                    "manage_texts",
                    "manage_buttons",
                    "manage_educational_content",
                    "manage_mcq_questions",
                    "manage_ministerial_questions",
                    "manage_files",
                    "preview_changes"
                ]
            },
            "services": {
                "name": "إدارة الخدمات",
                "description": "صلاحيات إدارة الخدمات والمختصين",
                "permissions": [
                    "manage_services",
                    "manage_specialists",
                    "view_service_orders",
                    "manage_service_categories",
                    "approve_services"
                ]
            },
            "moderation": {
                "name": "الإشراف",
                "description": "صلاحيات الإشراف على المستخدمين",
                "permissions": [
                    "moderate_users",
                    "view_user_activities",
                    "manage_reports",
                    "ban_users",
                    "delete_content"
                ]
            },
            "analytics": {
                "name": "التحليلات",
                "description": "صلاحيات عرض التحليلات والإحصائيات",
                "permissions": [
                    "view_analytics",
                    "view_detailed_stats",
                    "export_reports",
                    "view_user_data"
                ]
            },
            "technical": {
                "name": "الدعم الفني",
                "description": "صلاحيات الدعم الفني والصيانة",
                "permissions": [
                    "manage_technical_settings",
                    "view_system_logs",
                    "manage_database",
                    "perform_maintenance",
                    "debug_issues"
                ]
            }
        }
    
    def get_default_roles(self) -> Dict[str, Any]:
        """الأدوار الافتراضية"""
        return {
            "super_admin": {
                "name": "مشرف عام",
                "description": "صلاحيات كاملة لجميع أجزاء النظام",
                "level": 10,
                "categories": ["admin", "content", "services", "moderation", "analytics", "technical"],
                "restrictions": [],
                "created_date": datetime.now().isoformat(),
                "active": True
            },
            "content_admin": {
                "name": "مشرف المحتوى",
                "description": "إدارة المحتوى التعليمي والنصوص",
                "level": 7,
                "categories": ["content"],
                "restrictions": ["cannot_delete_main_content"],
                "created_date": datetime.now().isoformat(),
                "active": True
            },
            "services_admin": {
                "name": "مشرف الخدمات",
                "description": "إدارة الخدمات والمختصين",
                "level": 6,
                "categories": ["services"],
                "restrictions": ["cannot_delete_specialists"],
                "created_date": datetime.now().isoformat(),
                "active": True
            },
            "moderator": {
                "name": "مشرف",
                "description": "إشراف على المستخدمين والمحتوى",
                "level": 5,
                "categories": ["moderation"],
                "restrictions": ["cannot_ban_admins", "limited_user_data_access"],
                "created_date": datetime.now().isoformat(),
                "active": True
            },
            "analyst": {
                "name": "محلل",
                "description": "عرض التحليلات والإحصائيات",
                "level": 3,
                "categories": ["analytics"],
                "restrictions": ["read_only_access", "cannot_export_sensitive_data"],
                "created_date": datetime.now().isoformat(),
                "active": True
            },
            "support": {
                "name": "دعم فني",
                "description": "دعم فني محدود",
                "level": 4,
                "categories": ["technical"],
                "restrictions": ["cannot_access_database", "limited_system_access"],
                "created_date": datetime.now().isoformat(),
                "active": True
            }
        }
    
    def assign_role_to_user(self, user_id: int, role_name: str, assigned_by: int = None) -> bool:
        """تعيين دور لمستخدم"""
        try:
            if role_name not in self.roles_data:
                logger.error(f"❌ الدور غير موجود: {role_name}")
                return False
            
            role_data = self.roles_data[role_name]
            if not role_data.get("active", True):
                logger.error(f"❌ الدور غير نشط: {role_name}")
                return False
            
            # إنشاء أو تحديث صلاحيات المستخدم
            if user_id not in self.user_permissions:
                self.user_permissions[user_id] = {
                    "user_id": user_id,
                    "roles": [],
                    "custom_permissions": [],
                    "restrictions": [],
                    "assigned_date": datetime.now().isoformat(),
                    "assigned_by": assigned_by,
                    "active": True
                }
            
            # إضافة الدور إذا لم يكن موجوداً
            if role_name not in self.user_permissions[user_id]["roles"]:
                self.user_permissions[user_id]["roles"].append(role_name)
                self.user_permissions[user_id]["last_updated"] = datetime.now().isoformat()
                self.user_permissions[user_id]["updated_by"] = assigned_by
            
            # حفظ التغييرات
            success = firebase_manager.save_user_permissions(self.user_permissions)
            if success:
                logger.info(f"✅ تم تعيين الدور {role_name} للمستخدم {user_id}")
            return success
            
        except Exception as e:
            logger.error(f"❌ خطأ في تعيين الدور: {e}")
            return False
    
    def remove_role_from_user(self, user_id: int, role_name: str, removed_by: int = None) -> bool:
        """إزالة دور من مستخدم"""
        try:
            if user_id not in self.user_permissions:
                return False
            
            user_data = self.user_permissions[user_id]
            if role_name in user_data["roles"]:
                user_data["roles"].remove(role_name)
                user_data["last_updated"] = datetime.now().isoformat()
                user_data["updated_by"] = removed_by
                
                success = firebase_manager.save_user_permissions(self.user_permissions)
                if success:
                    logger.info(f"✅ تم إزالة الدور {role_name} من المستخدم {user_id}")
                return success
            
            return False
            
        except Exception as e:
            logger.error(f"❌ خطأ في إزالة الدور: {e}")
            return False
    
    def add_custom_permission(self, user_id: int, permission: str, added_by: int = None) -> bool:
        """إضافة صلاحية مخصصة لمستخدم"""
        try:
            if user_id not in self.user_permissions:
                self.user_permissions[user_id] = {
                    "user_id": user_id,
                    "roles": [],
                    "custom_permissions": [],
                    "restrictions": [],
                    "assigned_date": datetime.now().isoformat(),
                    "assigned_by": added_by,
                    "active": True
                }
            
            user_data = self.user_permissions[user_id]
            if permission not in user_data["custom_permissions"]:
                user_data["custom_permissions"].append(permission)
                user_data["last_updated"] = datetime.now().isoformat()
                user_data["updated_by"] = added_by
                
                success = firebase_manager.save_user_permissions(self.user_permissions)
                if success:
                    logger.info(f"✅ تم إضافة الصلاحية المخصصة {permission} للمستخدم {user_id}")
                return success
            
            return False
            
        except Exception as e:
            logger.error(f"❌ خطأ في إضافة الصلاحية المخصصة: {e}")
            return False
    
    def has_permission(self, user_id: int, permission: str) -> bool:
        """التحقق من وجود صلاحية لمستخدم"""
        try:
            if user_id not in self.user_permissions:
                return False
            
            user_data = self.user_permissions[user_id]
            if not user_data.get("active", True):
                return False
            
            # التحقق من الصلاحيات المخصصة
            if permission in user_data.get("custom_permissions", []):
                return True
            
            # التحقق من صلاحيات الأدوار
            for role_name in user_data.get("roles", []):
                if role_name in self.roles_data:
                    role_data = self.roles_data[role_name]
                    if role_data.get("active", True):
                        # التحقق من فئات الصلاحيات
                        for category in role_data.get("categories", []):
                            if category in self.permissions_data:
                                category_permissions = self.permissions_data[category].get("permissions", [])
                                if permission in category_permissions:
                                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"❌ خطأ في التحقق من الصلاحية: {e}")
            return False
    
    def has_role(self, user_id: int, role_name: str) -> bool:
        """التحقق من وجود دور لمستخدم"""
        try:
            if user_id not in self.user_permissions:
                return False
            
            user_data = self.user_permissions[user_id]
            return role_name in user_data.get("roles", []) and user_data.get("active", True)
            
        except Exception as e:
            logger.error(f"❌ خطأ في التحقق من الدور: {e}")
            return False
    
    def get_user_permissions(self, user_id: int) -> Dict[str, Any]:
        """الحصول على صلاحيات مستخدم"""
        try:
            if user_id not in self.user_permissions:
                return {}
            
            user_data = self.user_permissions[user_id]
            if not user_data.get("active", True):
                return {}
            
            # جمع جميع الصلاحيات
            all_permissions = set(user_data.get("custom_permissions", []))
            
            for role_name in user_data.get("roles", []):
                if role_name in self.roles_data:
                    role_data = self.roles_data[role_name]
                    if role_data.get("active", True):
                        for category in role_data.get("categories", []):
                            if category in self.permissions_data:
                                category_permissions = self.permissions_data[category].get("permissions", [])
                                all_permissions.update(category_permissions)
            
            return {
                "user_id": user_id,
                "roles": user_data.get("roles", []),
                "custom_permissions": user_data.get("custom_permissions", []),
                "all_permissions": list(all_permissions),
                "restrictions": user_data.get("restrictions", []),
                "level": self._get_user_level(user_id),
                "active": user_data.get("active", True)
            }
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على صلاحيات المستخدم: {e}")
            return {}
    
    def get_user_level(self, user_id: int) -> int:
        """الحصول على مستوى المستخدم"""
        return self._get_user_level(user_id)
    
    def _get_user_level(self, user_id: int) -> int:
        """حساب مستوى المستخدم بناءً على أدواره"""
        try:
            if user_id not in self.user_permissions:
                return 0
            
            user_data = self.user_permissions[user_id]
            if not user_data.get("active", True):
                return 0
            
            max_level = 0
            for role_name in user_data.get("roles", []):
                if role_name in self.roles_data:
                    role_level = self.roles_data[role_name].get("level", 0)
                    max_level = max(max_level, role_level)
            
            return max_level
            
        except Exception as e:
            logger.error(f"❌ خطأ في حساب مستوى المستخدم: {e}")
            return 0
    
    def can_manage_user(self, admin_id: int, target_user_id: int) -> bool:
        """التحقق من إمكانية إدارة مستخدم آخر"""
        try:
            admin_level = self.get_user_level(admin_id)
            target_level = self.get_user_level(target_user_id)
            
            # المشرف يجب أن يكون مستواه أعلى من المستخدم المستهدف
            return admin_level > target_level
            
        except Exception as e:
            logger.error(f"❌ خطأ في التحقق من إمكانية الإدارة: {e}")
            return False
    
    def create_role(self, role_name: str, role_data: Dict[str, Any], created_by: int = None) -> bool:
        """إنشاء دور جديد"""
        try:
            if role_name in self.roles_data:
                logger.error(f"❌ الدور موجود بالفعل: {role_name}")
                return False
            
            role_data["created_date"] = datetime.now().isoformat()
            role_data["created_by"] = created_by
            role_data["active"] = True
            
            self.roles_data[role_name] = role_data
            
            success = firebase_manager.save_roles_data(self.roles_data)
            if success:
                logger.info(f"✅ تم إنشاء الدور: {role_name}")
            return success
            
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء الدور: {e}")
            return False
    
    def update_role(self, role_name: str, role_data: Dict[str, Any], updated_by: int = None) -> bool:
        """تحديث دور موجود"""
        try:
            if role_name not in self.roles_data:
                logger.error(f"❌ الدور غير موجود: {role_name}")
                return False
            
            role_data["last_updated"] = datetime.now().isoformat()
            role_data["updated_by"] = updated_by
            
            self.roles_data[role_name].update(role_data)
            
            success = firebase_manager.save_roles_data(self.roles_data)
            if success:
                logger.info(f"✅ تم تحديث الدور: {role_name}")
            return success
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحديث الدور: {e}")
            return False
    
    def delete_role(self, role_name: str, deleted_by: int = None) -> bool:
        """حذف دور"""
        try:
            if role_name not in self.roles_data:
                return False
            
            # إزالة الدور من جميع المستخدمين
            for user_id, user_data in self.user_permissions.items():
                if role_name in user_data.get("roles", []):
                    user_data["roles"].remove(role_name)
                    user_data["last_updated"] = datetime.now().isoformat()
                    user_data["updated_by"] = deleted_by
            
            # حذف الدور
            del self.roles_data[role_name]
            
            # حفظ التغييرات
            success1 = firebase_manager.save_roles_data(self.roles_data)
            success2 = firebase_manager.save_user_permissions(self.user_permissions)
            
            success = success1 and success2
            if success:
                logger.info(f"✅ تم حذف الدور: {role_name}")
            return success
            
        except Exception as e:
            logger.error(f"❌ خطأ في حذف الدور: {e}")
            return False
    
    def get_all_roles(self) -> Dict[str, Any]:
        """الحصول على جميع الأدوار"""
        return self.roles_data
    
    def get_all_permissions(self) -> Dict[str, Any]:
        """الحصول على جميع الصلاحيات"""
        return self.permissions_data
    
    def get_users_with_role(self, role_name: str) -> List[int]:
        """الحصول على المستخدمين الذين لديهم دور محدد"""
        try:
            users = []
            for user_id, user_data in self.user_permissions.items():
                if role_name in user_data.get("roles", []) and user_data.get("active", True):
                    users.append(user_id)
            return users
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على المستخدمين بالدور: {e}")
            return []
    
    def get_permissions_summary(self) -> Dict[str, Any]:
        """الحصول على ملخص الصلاحيات"""
        try:
            return {
                "total_roles": len(self.roles_data),
                "active_roles": len([r for r in self.roles_data.values() if r.get("active", True)]),
                "total_users_with_permissions": len(self.user_permissions),
                "active_users_with_permissions": len([u for u in self.user_permissions.values() if u.get("active", True)]),
                "permission_categories": len(self.permissions_data),
                "total_permissions": sum(len(cat.get("permissions", [])) for cat in self.permissions_data.values())
            }
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء ملخص الصلاحيات: {e}")
            return {}
    
    def reload_permissions(self):
        """إعادة تحميل الصلاحيات من Firebase"""
        self.load_permissions_from_firebase()

# إنشاء مثيل مدير الصلاحيات
permissions_manager = PermissionsManager()
