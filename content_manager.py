#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير المحتوى الديناميكي للبوت التعليمي
Dynamic Content Manager for Educational Bot

يدير إنشاء وتخصيص المحتوى والأقسام بشكل ديناميكي
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
from firebase_manager import firebase_manager

logger = logging.getLogger(__name__)

class ContentManager:
    """مدير المحتوى الديناميكي"""
    
    def __init__(self):
        self.educational_content = {}
        self.ministerial_questions = {}
        self.services_data = {}
        self.content_structure = {}
        self.load_content_from_firebase()
    
    def load_content_from_firebase(self):
        """تحميل المحتوى من Firebase"""
        try:
            self.educational_content = firebase_manager.get_educational_content()
            self.ministerial_questions = firebase_manager.get_ministerial_questions()
            self.services_data = firebase_manager.get_services_data()
            
            if not self.educational_content:
                self.educational_content = self.get_default_educational_content()
                firebase_manager.save_educational_content(self.educational_content)
            
            if not self.ministerial_questions:
                self.ministerial_questions = self.get_default_ministerial_questions()
                firebase_manager.save_ministerial_questions(self.ministerial_questions)
            
            if not self.services_data:
                self.services_data = self.get_default_services_data()
                firebase_manager.save_services_data(self.services_data)
            
            logger.info("✅ تم تحميل المحتوى من Firebase")
        except Exception as e:
            logger.error(f"❌ خطأ في تحميل المحتوى: {e}")
            self.educational_content = self.get_default_educational_content()
            self.ministerial_questions = self.get_default_ministerial_questions()
            self.services_data = self.get_default_services_data()
    
    def get_default_educational_content(self) -> Dict[str, Any]:
        """المحتوى التعليمي الافتراضي"""
        return {
            "structure": {
                "type": "hierarchical",
                "levels": ["stage", "subject", "topic", "resource"],
                "display_mode": "tree"
            },
            "stages": {
                "المرحلة الأولى": {
                    "id": "stage_1",
                    "order": 1,
                    "icon": "🎓",
                    "description": "المرحلة الأساسية في التخصص",
                    "subjects": {
                        "الفيزياء الطبية": {
                            "id": "medical_physics_1",
                            "order": 1,
                            "icon": "⚛️",
                            "description": "أساسيات الفيزياء الطبية",
                            "topics": {
                                "مقدمة في الفيزياء الطبية": {
                                    "id": "intro_medical_physics",
                                    "order": 1,
                                    "resources": {
                                        "books": [
                                            {
                                                "title": "كتاب الفيزياء الطبية الأساسية",
                                                "author": "د. أحمد محمد",
                                                "type": "pdf",
                                                "url": "",
                                                "description": "مقدمة شاملة للفيزياء الطبية"
                                            }
                                        ],
                                        "videos": [],
                                        "exercises": [],
                                        "notes": []
                                    }
                                }
                            }
                        },
                        "التشريح": {
                            "id": "anatomy_1",
                            "order": 2,
                            "icon": "🦴",
                            "description": "علم التشريح البشري",
                            "topics": {
                                "التشريح العام": {
                                    "id": "general_anatomy",
                                    "order": 1,
                                    "resources": {
                                        "books": [
                                            {
                                                "title": "أطلس التشريح البشري",
                                                "author": "د. فاطمة علي",
                                                "type": "pdf",
                                                "url": "",
                                                "description": "أطلس مصور للتشريح"
                                            }
                                        ],
                                        "videos": [],
                                        "exercises": [],
                                        "notes": []
                                    }
                                }
                            }
                        }
                    }
                },
                "المرحلة الثانية": {
                    "id": "stage_2",
                    "order": 2,
                    "icon": "📚",
                    "description": "المرحلة المتوسطة في التخصص",
                    "subjects": {
                        "تقنيات التصوير الطبي": {
                            "id": "medical_imaging",
                            "order": 1,
                            "icon": "📷",
                            "description": "تقنيات التصوير الطبي المختلفة",
                            "topics": {}
                        }
                    }
                }
            },
            "settings": {
                "allow_user_suggestions": True,
                "show_progress": True,
                "enable_bookmarks": True,
                "download_enabled": True
            }
        }
    
    def get_default_ministerial_questions(self) -> Dict[str, Any]:
        """الأسئلة الوزارية الافتراضية"""
        return {
            "structure": {
                "type": "subject_year",
                "categories": ["subject", "year", "exam_type"],
                "display_mode": "grid"
            },
            "subjects": {
                "الفيزياء الطبية": {
                    "id": "medical_physics_questions",
                    "icon": "⚛️",
                    "years": {
                        "2023": {
                            "exams": {
                                "الدور الأول": {
                                    "id": "first_round_2023",
                                    "date": "2023-06-15",
                                    "questions_count": 25,
                                    "pdf_url": "",
                                    "solutions_url": "",
                                    "mcq_questions": []
                                },
                                "الدور الثاني": {
                                    "id": "second_round_2023",
                                    "date": "2023-09-15",
                                    "questions_count": 25,
                                    "pdf_url": "",
                                    "solutions_url": "",
                                    "mcq_questions": []
                                }
                            }
                        },
                        "2022": {
                            "exams": {
                                "الدور الأول": {
                                    "id": "first_round_2022",
                                    "date": "2022-06-15",
                                    "questions_count": 25,
                                    "pdf_url": "",
                                    "solutions_url": "",
                                    "mcq_questions": []
                                }
                            }
                        }
                    }
                }
            },
            "settings": {
                "enable_practice_mode": True,
                "show_solutions": True,
                "time_limit_enabled": True,
                "randomize_questions": True
            }
        }
    
    def get_default_services_data(self) -> Dict[str, Any]:
        """بيانات الخدمات الافتراضية"""
        return {
            "structure": {
                "type": "category_service",
                "categories": ["category", "service"],
                "display_mode": "cards"
            },
            "categories": {
                "التصميم والجرافيك": {
                    "id": "design_category",
                    "icon": "🎨",
                    "description": "خدمات التصميم الجرافيكي والإبداعي",
                    "services": {
                        "تصميم الشعارات": {
                            "id": "logo_design",
                            "icon": "🏷️",
                            "description": "تصميم شعارات احترافية ومميزة",
                            "price_range": "50-200 دولار",
                            "delivery_time": "3-7 أيام",
                            "specialists": [
                                {
                                    "name": "أحمد محمد",
                                    "title": "مصمم جرافيك",
                                    "experience": "5 سنوات",
                                    "contact": "@ahmed_designer"
                                }
                            ],
                            "portfolio": [],
                            "features": [
                                "تصميم أصلي 100%",
                                "ملفات عالية الجودة",
                                "تعديلات مجانية",
                                "تسليم سريع"
                            ]
                        }
                    }
                },
                "البرمجة والتطوير": {
                    "id": "programming_category",
                    "icon": "💻",
                    "description": "خدمات البرمجة وتطوير التطبيقات",
                    "services": {
                        "تطوير المواقع": {
                            "id": "web_development",
                            "icon": "🌐",
                            "description": "تطوير مواقع ويب احترافية ومتجاوبة",
                            "price_range": "200-1000 دولار",
                            "delivery_time": "1-4 أسابيع",
                            "specialists": [
                                {
                                    "name": "محمد أحمد",
                                    "title": "مطور ويب",
                                    "experience": "7 سنوات",
                                    "contact": "@mohammed_dev"
                                }
                            ],
                            "portfolio": [],
                            "features": [
                                "تصميم متجاوب",
                                "سرعة تحميل عالية",
                                "SEO محسن",
                                "دعم فني مجاني"
                            ]
                        }
                    }
                }
            },
            "settings": {
                "enable_quotes": True,
                "show_portfolio": True,
                "enable_reviews": True,
                "contact_method": "telegram"
            }
        }
    
    # ==================== إدارة المحتوى التعليمي ====================
    
    def add_stage(self, stage_name: str, stage_data: Dict[str, Any]) -> bool:
        """إضافة مرحلة دراسية جديدة"""
        try:
            if "stages" not in self.educational_content:
                self.educational_content["stages"] = {}
            
            stage_id = stage_data.get("id", f"stage_{len(self.educational_content['stages']) + 1}")
            stage_data["id"] = stage_id
            stage_data["created_date"] = datetime.now().isoformat()
            
            self.educational_content["stages"][stage_name] = stage_data
            
            success = firebase_manager.save_educational_content(self.educational_content)
            if success:
                logger.info(f"✅ تم إضافة المرحلة: {stage_name}")
            return success
        except Exception as e:
            logger.error(f"❌ خطأ في إضافة المرحلة: {e}")
            return False
    
    def add_subject(self, stage_name: str, subject_name: str, subject_data: Dict[str, Any]) -> bool:
        """إضافة مادة جديدة لمرحلة"""
        try:
            if stage_name not in self.educational_content.get("stages", {}):
                return False
            
            if "subjects" not in self.educational_content["stages"][stage_name]:
                self.educational_content["stages"][stage_name]["subjects"] = {}
            
            subject_id = subject_data.get("id", f"subject_{len(self.educational_content['stages'][stage_name]['subjects']) + 1}")
            subject_data["id"] = subject_id
            subject_data["created_date"] = datetime.now().isoformat()
            
            self.educational_content["stages"][stage_name]["subjects"][subject_name] = subject_data
            
            success = firebase_manager.save_educational_content(self.educational_content)
            if success:
                logger.info(f"✅ تم إضافة المادة: {subject_name} للمرحلة: {stage_name}")
            return success
        except Exception as e:
            logger.error(f"❌ خطأ في إضافة المادة: {e}")
            return False
    
    def add_topic(self, stage_name: str, subject_name: str, topic_name: str, topic_data: Dict[str, Any]) -> bool:
        """إضافة موضوع جديد لمادة"""
        try:
            stages = self.educational_content.get("stages", {})
            if stage_name not in stages:
                return False
            
            subjects = stages[stage_name].get("subjects", {})
            if subject_name not in subjects:
                return False
            
            if "topics" not in subjects[subject_name]:
                subjects[subject_name]["topics"] = {}
            
            topic_id = topic_data.get("id", f"topic_{len(subjects[subject_name]['topics']) + 1}")
            topic_data["id"] = topic_id
            topic_data["created_date"] = datetime.now().isoformat()
            
            subjects[subject_name]["topics"][topic_name] = topic_data
            
            success = firebase_manager.save_educational_content(self.educational_content)
            if success:
                logger.info(f"✅ تم إضافة الموضوع: {topic_name}")
            return success
        except Exception as e:
            logger.error(f"❌ خطأ في إضافة الموضوع: {e}")
            return False
    
    def add_resource(self, stage_name: str, subject_name: str, topic_name: str, 
                    resource_type: str, resource_data: Dict[str, Any]) -> bool:
        """إضافة مورد جديد لموضوع"""
        try:
            stages = self.educational_content.get("stages", {})
            if stage_name not in stages:
                return False
            
            subjects = stages[stage_name].get("subjects", {})
            if subject_name not in subjects:
                return False
            
            topics = subjects[subject_name].get("topics", {})
            if topic_name not in topics:
                return False
            
            if "resources" not in topics[topic_name]:
                topics[topic_name]["resources"] = {}
            
            if resource_type not in topics[topic_name]["resources"]:
                topics[topic_name]["resources"][resource_type] = []
            
            resource_data["id"] = f"resource_{datetime.now().timestamp()}"
            resource_data["added_date"] = datetime.now().isoformat()
            
            topics[topic_name]["resources"][resource_type].append(resource_data)
            
            success = firebase_manager.save_educational_content(self.educational_content)
            if success:
                logger.info(f"✅ تم إضافة المورد: {resource_type}")
            return success
        except Exception as e:
            logger.error(f"❌ خطأ في إضافة المورد: {e}")
            return False
    
    # ==================== إدارة الخدمات ====================
    
    def add_service_category(self, category_name: str, category_data: Dict[str, Any]) -> bool:
        """إضافة فئة خدمات جديدة"""
        try:
            if "categories" not in self.services_data:
                self.services_data["categories"] = {}
            
            category_id = category_data.get("id", f"category_{len(self.services_data['categories']) + 1}")
            category_data["id"] = category_id
            category_data["created_date"] = datetime.now().isoformat()
            
            self.services_data["categories"][category_name] = category_data
            
            success = firebase_manager.save_services_data(self.services_data)
            if success:
                logger.info(f"✅ تم إضافة فئة الخدمات: {category_name}")
            return success
        except Exception as e:
            logger.error(f"❌ خطأ في إضافة فئة الخدمات: {e}")
            return False
    
    def add_service(self, category_name: str, service_name: str, service_data: Dict[str, Any]) -> bool:
        """إضافة خدمة جديدة لفئة"""
        try:
            if category_name not in self.services_data.get("categories", {}):
                return False
            
            if "services" not in self.services_data["categories"][category_name]:
                self.services_data["categories"][category_name]["services"] = {}
            
            service_id = service_data.get("id", f"service_{len(self.services_data['categories'][category_name]['services']) + 1}")
            service_data["id"] = service_id
            service_data["created_date"] = datetime.now().isoformat()
            
            self.services_data["categories"][category_name]["services"][service_name] = service_data
            
            success = firebase_manager.save_services_data(self.services_data)
            if success:
                logger.info(f"✅ تم إضافة الخدمة: {service_name}")
            return success
        except Exception as e:
            logger.error(f"❌ خطأ في إضافة الخدمة: {e}")
            return False
    
    # ==================== وظائف مساعدة ====================
    
    def get_content_structure(self, content_type: str) -> Dict[str, Any]:
        """الحصول على هيكل المحتوى"""
        if content_type == "educational":
            return self.educational_content
        elif content_type == "ministerial":
            return self.ministerial_questions
        elif content_type == "services":
            return self.services_data
        return {}
    
    def reload_content(self):
        """إعادة تحميل المحتوى من Firebase"""
        self.load_content_from_firebase()

# إنشاء مثيل مدير المحتوى
content_manager = ContentManager()
