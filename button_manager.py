#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير الأزرار الديناميكية للبوت التعليمي
Dynamic Button Manager for Educational Bot

يدير إنشاء وتخصيص الأزرار بشكل ديناميكي
"""

import logging
from typing import Dict, Any, Optional, List, Union
from telebot import types
from firebase_manager import firebase_manager

logger = logging.getLogger(__name__)

class ButtonManager:
    """مدير الأزرار الديناميكية"""
    
    def __init__(self):
        self.buttons_config = {}
        self.load_buttons_from_firebase()
    
    def load_buttons_from_firebase(self):
        """تحميل تكوين الأزرار من Firebase"""
        try:
            self.buttons_config = firebase_manager.get_dynamic_buttons()
            if not self.buttons_config:
                self.buttons_config = self.get_default_buttons_config()
                firebase_manager.save_dynamic_buttons(self.buttons_config)
            logger.info("✅ تم تحميل تكوين الأزرار من Firebase")
        except Exception as e:
            logger.error(f"❌ خطأ في تحميل تكوين الأزرار: {e}")
            self.buttons_config = self.get_default_buttons_config()
    
    def get_default_buttons_config(self) -> Dict[str, Any]:
        """التكوين الافتراضي للأزرار"""
        return {
            "main_menu": {
                "type": "keyboard",
                "layout": "2x2+1",  # تخطيط الأزرار
                "buttons": [
                    {
                        "text": "📝 الترجمة",
                        "action": "section",
                        "target": "translation",
                        "row": 1,
                        "position": 1
                    },
                    {
                        "text": "📖 المحتوى العلمي",
                        "action": "section",
                        "target": "educational_content",
                        "row": 1,
                        "position": 2
                    },
                    {
                        "text": "🗂️ المواد الوزارية",
                        "action": "section",
                        "target": "ministerial_materials",
                        "row": 2,
                        "position": 1
                    },
                    {
                        "text": "🛠️ الخدمات",
                        "action": "section",
                        "target": "services",
                        "row": 2,
                        "position": 2
                    },
                    {
                        "text": "💡 اقتراح فكرة جديدة",
                        "action": "section",
                        "target": "suggest_idea",
                        "row": 3,
                        "position": 1,
                        "width": "full"
                    }
                ],
                "admin_button": {
                    "text": "🔧 لوحة التحكم",
                    "action": "admin_panel",
                    "target": "admin_control",
                    "admin_only": True
                }
            },
            "admin_panel": {
                "type": "inline",
                "layout": "2x5",
                "buttons": [
                    {
                        "text": "📊 الإحصائيات",
                        "callback_data": "admin_stats",
                        "row": 1,
                        "position": 1
                    },
                    {
                        "text": "👥 إدارة المستخدمين",
                        "callback_data": "admin_users",
                        "row": 1,
                        "position": 2
                    },
                    {
                        "text": "💡 الأفكار المقترحة",
                        "callback_data": "admin_ideas",
                        "row": 2,
                        "position": 1
                    },
                    {
                        "text": "📚 إدارة المحتوى",
                        "callback_data": "admin_content",
                        "row": 2,
                        "position": 2
                    },
                    {
                        "text": "🔤 إدارة النصوص",
                        "callback_data": "admin_text",
                        "row": 3,
                        "position": 1
                    },
                    {
                        "text": "🔘 إدارة الأزرار",
                        "callback_data": "admin_buttons",
                        "row": 3,
                        "position": 2
                    },
                    {
                        "text": "❓ إدارة الأسئلة",
                        "callback_data": "admin_mcq",
                        "row": 4,
                        "position": 1
                    },
                    {
                        "text": "👨‍💼 إدارة المشرفين",
                        "callback_data": "admin_admins",
                        "row": 4,
                        "position": 2
                    },
                    {
                        "text": "📢 رسالة جماعية",
                        "callback_data": "admin_broadcast",
                        "row": 5,
                        "position": 1
                    },
                    {
                        "text": "💾 النسخ الاحتياطي",
                        "callback_data": "admin_backup",
                        "row": 5,
                        "position": 2
                    },
                    {
                        "text": "⚙️ إعدادات البوت",
                        "callback_data": "admin_settings",
                        "row": 6,
                        "position": 1
                    },
                    {
                        "text": "📋 سجلات النشاط",
                        "callback_data": "admin_logs",
                        "row": 6,
                        "position": 2
                    }
                ]
            },
            "back_button": {
                "type": "keyboard",
                "buttons": [
                    {
                        "text": "🔙 العودة للقائمة الرئيسية",
                        "action": "back_to_main",
                        "target": "main_menu"
                    }
                ]
            },
            "admin_back": {
                "type": "inline",
                "buttons": [
                    {
                        "text": "🔙 العودة للوحة التحكم",
                        "callback_data": "admin_back"
                    }
                ]
            }
        }
    
    def create_keyboard(self, config_name: str, user_id: Optional[int] = None, **kwargs) -> Union[types.ReplyKeyboardMarkup, types.InlineKeyboardMarkup]:
        """إنشاء كيبورد بناءً على التكوين"""
        try:
            if config_name not in self.buttons_config:
                logger.warning(f"⚠️ تكوين الأزرار غير موجود: {config_name}")
                return self.create_default_keyboard()
            
            config = self.buttons_config[config_name]
            button_type = config.get("type", "keyboard")
            
            if button_type == "keyboard":
                return self._create_reply_keyboard(config, user_id, **kwargs)
            elif button_type == "inline":
                return self._create_inline_keyboard(config, user_id, **kwargs)
            else:
                logger.error(f"❌ نوع أزرار غير مدعوم: {button_type}")
                return self.create_default_keyboard()
                
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء الكيبورد: {e}")
            return self.create_default_keyboard()
    
    def _create_reply_keyboard(self, config: Dict[str, Any], user_id: Optional[int] = None, **kwargs) -> types.ReplyKeyboardMarkup:
        """إنشاء Reply Keyboard"""
        keyboard = types.ReplyKeyboardMarkup(resize_keyboard=True, row_width=2)
        
        buttons = config.get("buttons", [])
        layout = config.get("layout", "2x2")
        
        # ترتيب الأزرار حسب الصفوف والمواضع
        rows = {}
        for button in buttons:
            row = button.get("row", 1)
            if row not in rows:
                rows[row] = []
            rows[row].append(button)
        
        # إضافة الأزرار للكيبورد
        for row_num in sorted(rows.keys()):
            row_buttons = sorted(rows[row_num], key=lambda x: x.get("position", 1))
            
            if len(row_buttons) == 1 and row_buttons[0].get("width") == "full":
                # زر بعرض كامل
                btn = types.KeyboardButton(row_buttons[0]["text"])
                keyboard.add(btn)
            elif len(row_buttons) == 2:
                # زرين في صف واحد
                btn1 = types.KeyboardButton(row_buttons[0]["text"])
                btn2 = types.KeyboardButton(row_buttons[1]["text"])
                keyboard.add(btn1, btn2)
            else:
                # أزرار متعددة
                btn_objects = [types.KeyboardButton(btn["text"]) for btn in row_buttons]
                keyboard.add(*btn_objects)
        
        # إضافة زر المشرف إذا كان المستخدم مشرفاً
        if user_id and "admin_button" in config:
            from educational_bot_fixed import ADMIN_IDS  # استيراد محلي لتجنب التبعية الدائرية
            if user_id in ADMIN_IDS:
                admin_btn = types.KeyboardButton(config["admin_button"]["text"])
                keyboard.add(admin_btn)
        
        return keyboard
    
    def _create_inline_keyboard(self, config: Dict[str, Any], user_id: Optional[int] = None, **kwargs) -> types.InlineKeyboardMarkup:
        """إنشاء Inline Keyboard"""
        keyboard = types.InlineKeyboardMarkup(row_width=2)
        
        buttons = config.get("buttons", [])
        
        # ترتيب الأزرار حسب الصفوف والمواضع
        rows = {}
        for button in buttons:
            row = button.get("row", 1)
            if row not in rows:
                rows[row] = []
            rows[row].append(button)
        
        # إضافة الأزرار للكيبورد
        for row_num in sorted(rows.keys()):
            row_buttons = sorted(rows[row_num], key=lambda x: x.get("position", 1))
            
            btn_objects = []
            for btn_config in row_buttons:
                text = btn_config["text"]
                callback_data = btn_config.get("callback_data", "")
                url = btn_config.get("url", None)
                
                # تطبيق المتغيرات الديناميكية
                if kwargs:
                    text = text.format(**kwargs)
                    callback_data = callback_data.format(**kwargs) if callback_data else ""
                
                if url:
                    btn = types.InlineKeyboardButton(text, url=url)
                else:
                    btn = types.InlineKeyboardButton(text, callback_data=callback_data)
                
                btn_objects.append(btn)
            
            if len(btn_objects) == 1:
                keyboard.add(btn_objects[0])
            elif len(btn_objects) == 2:
                keyboard.add(btn_objects[0], btn_objects[1])
            else:
                keyboard.add(*btn_objects)
        
        return keyboard
    
    def create_default_keyboard(self) -> types.ReplyKeyboardMarkup:
        """إنشاء كيبورد افتراضي في حالة الخطأ"""
        keyboard = types.ReplyKeyboardMarkup(resize_keyboard=True)
        btn = types.KeyboardButton("🏠 القائمة الرئيسية")
        keyboard.add(btn)
        return keyboard
    
    def add_button_config(self, config_name: str, config: Dict[str, Any]) -> bool:
        """إضافة تكوين أزرار جديد"""
        try:
            self.buttons_config[config_name] = config
            success = firebase_manager.save_dynamic_buttons(self.buttons_config)
            if success:
                logger.info(f"✅ تم إضافة تكوين الأزرار: {config_name}")
            return success
        except Exception as e:
            logger.error(f"❌ خطأ في إضافة تكوين الأزرار: {e}")
            return False
    
    def update_button_config(self, config_name: str, config: Dict[str, Any]) -> bool:
        """تحديث تكوين أزرار موجود"""
        try:
            if config_name in self.buttons_config:
                self.buttons_config[config_name].update(config)
            else:
                self.buttons_config[config_name] = config
            
            success = firebase_manager.save_dynamic_buttons(self.buttons_config)
            if success:
                logger.info(f"✅ تم تحديث تكوين الأزرار: {config_name}")
            return success
        except Exception as e:
            logger.error(f"❌ خطأ في تحديث تكوين الأزرار: {e}")
            return False
    
    def remove_button_config(self, config_name: str) -> bool:
        """حذف تكوين أزرار"""
        try:
            if config_name in self.buttons_config:
                del self.buttons_config[config_name]
                success = firebase_manager.save_dynamic_buttons(self.buttons_config)
                if success:
                    logger.info(f"✅ تم حذف تكوين الأزرار: {config_name}")
                return success
            else:
                logger.warning(f"⚠️ تكوين الأزرار غير موجود: {config_name}")
                return False
        except Exception as e:
            logger.error(f"❌ خطأ في حذف تكوين الأزرار: {e}")
            return False
    
    def get_button_config(self, config_name: str) -> Optional[Dict[str, Any]]:
        """الحصول على تكوين أزرار محدد"""
        return self.buttons_config.get(config_name)
    
    def get_all_button_configs(self) -> Dict[str, Any]:
        """الحصول على جميع تكوينات الأزرار"""
        return self.buttons_config.copy()
    
    def reload_buttons(self):
        """إعادة تحميل تكوين الأزرار من Firebase"""
        self.load_buttons_from_firebase()

# إنشاء مثيل مدير الأزرار
button_manager = ButtonManager()
