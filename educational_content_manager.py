#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير المحتوى التعليمي التفاعلي للبوت التعليمي
Interactive Educational Content Manager for Educational Bot

يدير إنشاء وتخصيص المحتوى التعليمي بهيكل متعدد المستويات
"""

import logging
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime
from firebase_manager import firebase_manager
from button_manager import button_manager

logger = logging.getLogger(__name__)

class EducationalContentManager:
    """مدير المحتوى التعليمي التفاعلي"""
    
    def __init__(self):
        self.content_structure = {}
        self.navigation_buttons = {}
        self.content_settings = {}
        self.load_content_from_firebase()
    
    def load_content_from_firebase(self):
        """تحميل المحتوى التعليمي من Firebase"""
        try:
            self.content_structure = firebase_manager.get_educational_content() or self.get_default_structure()
            self.navigation_buttons = firebase_manager.get_navigation_buttons() or {}
            self.content_settings = firebase_manager.get_content_settings() or self.get_default_settings()
            logger.info("✅ تم تحميل المحتوى التعليمي من Firebase")
        except Exception as e:
            logger.error(f"❌ خطأ في تحميل المحتوى التعليمي: {e}")
            self.content_structure = self.get_default_structure()
            self.navigation_buttons = {}
            self.content_settings = self.get_default_settings()
    
    def get_default_structure(self) -> Dict[str, Any]:
        """الهيكل الافتراضي للمحتوى التعليمي"""
        return {
            "hierarchy": {
                "levels": ["stage", "subject", "chapter", "lesson", "resource"],
                "level_names": {
                    "stage": "المرحلة",
                    "subject": "المادة", 
                    "chapter": "الفصل",
                    "lesson": "الدرس",
                    "resource": "المورد"
                },
                "icons": {
                    "stage": "🎓",
                    "subject": "📚",
                    "chapter": "📖",
                    "lesson": "📝",
                    "resource": "📄"
                }
            },
            "stages": {
                "المرحلة الأولى": {
                    "id": "stage_1",
                    "order": 1,
                    "icon": "🎓",
                    "description": "المرحلة الأساسية في التخصص الطبي",
                    "color": "#3498db",
                    "active": True,
                    "subjects": {
                        "الفيزياء الطبية": {
                            "id": "medical_physics_1",
                            "order": 1,
                            "icon": "⚛️",
                            "description": "أساسيات الفيزياء الطبية والإشعاع",
                            "color": "#e74c3c",
                            "prerequisites": [],
                            "estimated_hours": 120,
                            "difficulty": "متوسط",
                            "active": True,
                            "chapters": {
                                "مقدمة في الفيزياء الطبية": {
                                    "id": "intro_medical_physics",
                                    "order": 1,
                                    "icon": "🔬",
                                    "description": "المفاهيم الأساسية في الفيزياء الطبية",
                                    "estimated_hours": 20,
                                    "active": True,
                                    "lessons": {
                                        "تاريخ الفيزياء الطبية": {
                                            "id": "history_medical_physics",
                                            "order": 1,
                                            "icon": "📜",
                                            "description": "تطور علم الفيزياء الطبية عبر التاريخ",
                                            "estimated_time": "45 دقيقة",
                                            "difficulty": "سهل",
                                            "active": True,
                                            "resources": {
                                                "videos": [
                                                    {
                                                        "title": "تاريخ الفيزياء الطبية",
                                                        "description": "فيديو تعليمي عن تطور الفيزياء الطبية",
                                                        "url": "",
                                                        "duration": "15:30",
                                                        "type": "video",
                                                        "quality": "HD",
                                                        "language": "عربي",
                                                        "subtitles": True
                                                    }
                                                ],
                                                "documents": [
                                                    {
                                                        "title": "ملزمة تاريخ الفيزياء الطبية",
                                                        "description": "ملزمة شاملة عن تاريخ وتطور الفيزياء الطبية",
                                                        "url": "",
                                                        "type": "pdf",
                                                        "pages": 25,
                                                        "size": "2.5 MB",
                                                        "language": "عربي"
                                                    }
                                                ],
                                                "exercises": [
                                                    {
                                                        "title": "تمارين تاريخ الفيزياء الطبية",
                                                        "description": "مجموعة تمارين وأسئلة تطبيقية",
                                                        "questions_count": 10,
                                                        "type": "mcq",
                                                        "difficulty": "سهل"
                                                    }
                                                ],
                                                "links": [
                                                    {
                                                        "title": "موقع الجمعية الدولية للفيزياء الطبية",
                                                        "description": "موقع رسمي للجمعية الدولية",
                                                        "url": "https://www.iomp.org",
                                                        "type": "external"
                                                    }
                                                ]
                                            }
                                        },
                                        "أساسيات الإشعاع": {
                                            "id": "radiation_basics",
                                            "order": 2,
                                            "icon": "☢️",
                                            "description": "المفاهيم الأساسية للإشعاع وأنواعه",
                                            "estimated_time": "60 دقيقة",
                                            "difficulty": "متوسط",
                                            "active": True,
                                            "resources": {
                                                "videos": [],
                                                "documents": [],
                                                "exercises": [],
                                                "links": []
                                            }
                                        }
                                    }
                                },
                                "أجهزة التصوير الطبي": {
                                    "id": "medical_imaging_devices",
                                    "order": 2,
                                    "icon": "📷",
                                    "description": "أجهزة التصوير الطبي المختلفة",
                                    "estimated_hours": 30,
                                    "active": True,
                                    "lessons": {}
                                }
                            }
                        },
                        "التشريح البشري": {
                            "id": "human_anatomy",
                            "order": 2,
                            "icon": "🦴",
                            "description": "علم التشريح البشري والأجهزة الحيوية",
                            "color": "#2ecc71",
                            "active": True,
                            "chapters": {}
                        }
                    }
                },
                "المرحلة الثانية": {
                    "id": "stage_2",
                    "order": 2,
                    "icon": "📚",
                    "description": "المرحلة المتوسطة في التخصص",
                    "color": "#9b59b6",
                    "active": True,
                    "subjects": {}
                }
            },
            "metadata": {
                "total_stages": 2,
                "total_subjects": 2,
                "total_chapters": 2,
                "total_lessons": 2,
                "total_resources": 4,
                "last_updated": datetime.now().isoformat(),
                "version": "1.0"
            }
        }
    
    def get_default_settings(self) -> Dict[str, Any]:
        """الإعدادات الافتراضية للمحتوى التعليمي"""
        return {
            "display": {
                "show_progress": True,
                "show_estimated_time": True,
                "show_difficulty": True,
                "show_prerequisites": True,
                "items_per_page": 10,
                "default_view": "list"  # list, grid, tree
            },
            "navigation": {
                "enable_breadcrumbs": True,
                "enable_quick_navigation": True,
                "enable_search": True,
                "enable_bookmarks": True,
                "auto_generate_buttons": True
            },
            "content": {
                "allow_user_notes": True,
                "allow_user_ratings": True,
                "track_progress": True,
                "require_completion": False,
                "enable_certificates": False
            },
            "access": {
                "require_login": False,
                "stage_based_access": False,
                "prerequisite_enforcement": False,
                "time_based_access": False
            }
        }
    
    def get_stages(self) -> List[Dict[str, Any]]:
        """الحصول على قائمة المراحل"""
        try:
            stages = []
            for stage_name, stage_data in self.content_structure.get("stages", {}).items():
                if stage_data.get("active", True):
                    stages.append({
                        "name": stage_name,
                        "data": stage_data
                    })
            
            # ترتيب حسب الأولوية
            stages.sort(key=lambda x: x["data"].get("order", 999))
            return stages
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على المراحل: {e}")
            return []
    
    def get_stage_subjects(self, stage_name: str) -> List[Dict[str, Any]]:
        """الحصول على مواد مرحلة محددة"""
        try:
            stage = self.content_structure.get("stages", {}).get(stage_name, {})
            subjects = []
            
            for subject_name, subject_data in stage.get("subjects", {}).items():
                if subject_data.get("active", True):
                    subjects.append({
                        "name": subject_name,
                        "data": subject_data
                    })
            
            subjects.sort(key=lambda x: x["data"].get("order", 999))
            return subjects
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على مواد المرحلة: {e}")
            return []
    
    def get_subject_chapters(self, stage_name: str, subject_name: str) -> List[Dict[str, Any]]:
        """الحصول على فصول مادة محددة"""
        try:
            subject = self.content_structure.get("stages", {}).get(stage_name, {}).get("subjects", {}).get(subject_name, {})
            chapters = []
            
            for chapter_name, chapter_data in subject.get("chapters", {}).items():
                if chapter_data.get("active", True):
                    chapters.append({
                        "name": chapter_name,
                        "data": chapter_data
                    })
            
            chapters.sort(key=lambda x: x["data"].get("order", 999))
            return chapters
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على فصول المادة: {e}")
            return []
    
    def get_chapter_lessons(self, stage_name: str, subject_name: str, chapter_name: str) -> List[Dict[str, Any]]:
        """الحصول على دروس فصل محدد"""
        try:
            chapter = (self.content_structure.get("stages", {})
                      .get(stage_name, {})
                      .get("subjects", {})
                      .get(subject_name, {})
                      .get("chapters", {})
                      .get(chapter_name, {}))
            
            lessons = []
            for lesson_name, lesson_data in chapter.get("lessons", {}).items():
                if lesson_data.get("active", True):
                    lessons.append({
                        "name": lesson_name,
                        "data": lesson_data
                    })
            
            lessons.sort(key=lambda x: x["data"].get("order", 999))
            return lessons
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على دروس الفصل: {e}")
            return []
    
    def get_lesson_resources(self, stage_name: str, subject_name: str, chapter_name: str, lesson_name: str) -> Dict[str, List[Dict[str, Any]]]:
        """الحصول على موارد درس محدد"""
        try:
            lesson = (self.content_structure.get("stages", {})
                     .get(stage_name, {})
                     .get("subjects", {})
                     .get(subject_name, {})
                     .get("chapters", {})
                     .get(chapter_name, {})
                     .get("lessons", {})
                     .get(lesson_name, {}))
            
            return lesson.get("resources", {})
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على موارد الدرس: {e}")
            return {}
    
    def add_stage(self, stage_name: str, stage_data: Dict[str, Any]) -> bool:
        """إضافة مرحلة جديدة"""
        try:
            if "stages" not in self.content_structure:
                self.content_structure["stages"] = {}
            
            # تحديد الترتيب التلقائي
            if "order" not in stage_data:
                max_order = max([s.get("order", 0) for s in self.content_structure["stages"].values()], default=0)
                stage_data["order"] = max_order + 1
            
            stage_data["id"] = stage_data.get("id", f"stage_{len(self.content_structure['stages']) + 1}")
            stage_data["created_date"] = datetime.now().isoformat()
            stage_data["active"] = True
            
            if "subjects" not in stage_data:
                stage_data["subjects"] = {}
            
            self.content_structure["stages"][stage_name] = stage_data
            self._update_metadata()
            
            success = firebase_manager.save_educational_content(self.content_structure)
            if success:
                logger.info(f"✅ تم إضافة المرحلة: {stage_name}")
                self._generate_navigation_buttons()
            return success
        except Exception as e:
            logger.error(f"❌ خطأ في إضافة المرحلة: {e}")
            return False
    
    def add_subject(self, stage_name: str, subject_name: str, subject_data: Dict[str, Any]) -> bool:
        """إضافة مادة جديدة"""
        try:
            if stage_name not in self.content_structure.get("stages", {}):
                return False
            
            stage = self.content_structure["stages"][stage_name]
            if "subjects" not in stage:
                stage["subjects"] = {}
            
            # تحديد الترتيب التلقائي
            if "order" not in subject_data:
                max_order = max([s.get("order", 0) for s in stage["subjects"].values()], default=0)
                subject_data["order"] = max_order + 1
            
            subject_data["id"] = subject_data.get("id", f"subject_{len(stage['subjects']) + 1}")
            subject_data["created_date"] = datetime.now().isoformat()
            subject_data["active"] = True
            
            if "chapters" not in subject_data:
                subject_data["chapters"] = {}
            
            stage["subjects"][subject_name] = subject_data
            self._update_metadata()
            
            success = firebase_manager.save_educational_content(self.content_structure)
            if success:
                logger.info(f"✅ تم إضافة المادة: {subject_name}")
                self._generate_navigation_buttons()
            return success
        except Exception as e:
            logger.error(f"❌ خطأ في إضافة المادة: {e}")
            return False
    
    def add_chapter(self, stage_name: str, subject_name: str, chapter_name: str, chapter_data: Dict[str, Any]) -> bool:
        """إضافة فصل جديد"""
        try:
            subject = (self.content_structure.get("stages", {})
                      .get(stage_name, {})
                      .get("subjects", {})
                      .get(subject_name, {}))
            
            if not subject:
                return False
            
            if "chapters" not in subject:
                subject["chapters"] = {}
            
            # تحديد الترتيب التلقائي
            if "order" not in chapter_data:
                max_order = max([c.get("order", 0) for c in subject["chapters"].values()], default=0)
                chapter_data["order"] = max_order + 1
            
            chapter_data["id"] = chapter_data.get("id", f"chapter_{len(subject['chapters']) + 1}")
            chapter_data["created_date"] = datetime.now().isoformat()
            chapter_data["active"] = True
            
            if "lessons" not in chapter_data:
                chapter_data["lessons"] = {}
            
            subject["chapters"][chapter_name] = chapter_data
            self._update_metadata()
            
            success = firebase_manager.save_educational_content(self.content_structure)
            if success:
                logger.info(f"✅ تم إضافة الفصل: {chapter_name}")
                self._generate_navigation_buttons()
            return success
        except Exception as e:
            logger.error(f"❌ خطأ في إضافة الفصل: {e}")
            return False
    
    def add_lesson(self, stage_name: str, subject_name: str, chapter_name: str, lesson_name: str, lesson_data: Dict[str, Any]) -> bool:
        """إضافة درس جديد"""
        try:
            chapter = (self.content_structure.get("stages", {})
                      .get(stage_name, {})
                      .get("subjects", {})
                      .get(subject_name, {})
                      .get("chapters", {})
                      .get(chapter_name, {}))
            
            if not chapter:
                return False
            
            if "lessons" not in chapter:
                chapter["lessons"] = {}
            
            # تحديد الترتيب التلقائي
            if "order" not in lesson_data:
                max_order = max([l.get("order", 0) for l in chapter["lessons"].values()], default=0)
                lesson_data["order"] = max_order + 1
            
            lesson_data["id"] = lesson_data.get("id", f"lesson_{len(chapter['lessons']) + 1}")
            lesson_data["created_date"] = datetime.now().isoformat()
            lesson_data["active"] = True
            
            if "resources" not in lesson_data:
                lesson_data["resources"] = {
                    "videos": [],
                    "documents": [],
                    "exercises": [],
                    "links": []
                }
            
            chapter["lessons"][lesson_name] = lesson_data
            self._update_metadata()
            
            success = firebase_manager.save_educational_content(self.content_structure)
            if success:
                logger.info(f"✅ تم إضافة الدرس: {lesson_name}")
                self._generate_navigation_buttons()
            return success
        except Exception as e:
            logger.error(f"❌ خطأ في إضافة الدرس: {e}")
            return False
    
    def add_resource(self, stage_name: str, subject_name: str, chapter_name: str, lesson_name: str, 
                    resource_type: str, resource_data: Dict[str, Any]) -> bool:
        """إضافة مورد جديد"""
        try:
            lesson = (self.content_structure.get("stages", {})
                     .get(stage_name, {})
                     .get("subjects", {})
                     .get(subject_name, {})
                     .get("chapters", {})
                     .get(chapter_name, {})
                     .get("lessons", {})
                     .get(lesson_name, {}))
            
            if not lesson:
                return False
            
            if "resources" not in lesson:
                lesson["resources"] = {}
            
            if resource_type not in lesson["resources"]:
                lesson["resources"][resource_type] = []
            
            resource_data["id"] = f"resource_{datetime.now().timestamp()}"
            resource_data["added_date"] = datetime.now().isoformat()
            
            lesson["resources"][resource_type].append(resource_data)
            self._update_metadata()
            
            success = firebase_manager.save_educational_content(self.content_structure)
            if success:
                logger.info(f"✅ تم إضافة المورد: {resource_type}")
            return success
        except Exception as e:
            logger.error(f"❌ خطأ في إضافة المورد: {e}")
            return False
    
    def _update_metadata(self):
        """تحديث البيانات الوصفية"""
        try:
            metadata = {
                "total_stages": 0,
                "total_subjects": 0,
                "total_chapters": 0,
                "total_lessons": 0,
                "total_resources": 0,
                "last_updated": datetime.now().isoformat(),
                "version": "1.0"
            }
            
            for stage_name, stage_data in self.content_structure.get("stages", {}).items():
                if stage_data.get("active", True):
                    metadata["total_stages"] += 1
                    
                    for subject_name, subject_data in stage_data.get("subjects", {}).items():
                        if subject_data.get("active", True):
                            metadata["total_subjects"] += 1
                            
                            for chapter_name, chapter_data in subject_data.get("chapters", {}).items():
                                if chapter_data.get("active", True):
                                    metadata["total_chapters"] += 1
                                    
                                    for lesson_name, lesson_data in chapter_data.get("lessons", {}).items():
                                        if lesson_data.get("active", True):
                                            metadata["total_lessons"] += 1
                                            
                                            for resource_type, resources in lesson_data.get("resources", {}).items():
                                                metadata["total_resources"] += len(resources)
            
            self.content_structure["metadata"] = metadata
        except Exception as e:
            logger.error(f"❌ خطأ في تحديث البيانات الوصفية: {e}")
    
    def _generate_navigation_buttons(self):
        """إنشاء أزرار التنقل التلقائية"""
        try:
            if not self.content_settings.get("navigation", {}).get("auto_generate_buttons", True):
                return
            
            # سيتم تطوير هذا لاحقاً
            pass
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء أزرار التنقل: {e}")
    
    def get_content_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات المحتوى"""
        return self.content_structure.get("metadata", {})
    
    def reload_content(self):
        """إعادة تحميل المحتوى من Firebase"""
        self.load_content_from_firebase()

# إنشاء مثيل مدير المحتوى التعليمي
educational_content_manager = EducationalContentManager()
