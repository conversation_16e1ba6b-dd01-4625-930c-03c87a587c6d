#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير المعاينة والاختبار للبوت التعليمي
Preview and Testing Manager for Educational Bot

يوفر نظام معاينة فوري للتغييرات واختبار الأقسام والأزرار
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
from telebot import types
from text_manager import text_manager
from button_manager import button_manager
from bot_settings_manager import bot_settings_manager

logger = logging.getLogger(__name__)

class PreviewManager:
    """مدير المعاينة والاختبار"""
    
    def __init__(self, bot):
        self.bot = bot
        self.preview_sessions = {}  # جلسات المعاينة النشطة
        self.test_environments = {}  # بيئات الاختبار
    
    def create_preview_session(self, user_id: int, preview_type: str, data: Dict[str, Any]) -> str:
        """إنشاء جلسة معاينة جديدة"""
        try:
            session_id = f"preview_{user_id}_{datetime.now().timestamp()}"
            
            session_data = {
                "id": session_id,
                "user_id": user_id,
                "type": preview_type,
                "data": data,
                "created_date": datetime.now().isoformat(),
                "status": "active",
                "interactions": []
            }
            
            self.preview_sessions[session_id] = session_data
            logger.info(f"✅ تم إنشاء جلسة معاينة: {session_id}")
            return session_id
            
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء جلسة المعاينة: {e}")
            return ""
    
    def preview_text_changes(self, call, section_key: str, text_key: str, new_text: str):
        """معاينة تغييرات النصوص"""
        try:
            # الحصول على النص الحالي
            current_text = text_manager.get_text(section_key, text_key)
            
            # إنشاء جلسة معاينة
            session_id = self.create_preview_session(
                call.from_user.id,
                "text_preview",
                {
                    "section_key": section_key,
                    "text_key": text_key,
                    "current_text": current_text,
                    "new_text": new_text
                }
            )
            
            keyboard = types.InlineKeyboardMarkup(row_width=2)
            
            btn_apply = types.InlineKeyboardButton(
                "✅ تطبيق التغيير",
                callback_data=f"preview_apply_text_{session_id}"
            )
            btn_edit = types.InlineKeyboardButton(
                "✏️ تعديل النص",
                callback_data=f"preview_edit_text_{session_id}"
            )
            btn_test = types.InlineKeyboardButton(
                "🧪 اختبار النص",
                callback_data=f"preview_test_text_{session_id}"
            )
            btn_cancel = types.InlineKeyboardButton(
                "❌ إلغاء",
                callback_data=f"preview_cancel_{session_id}"
            )
            
            keyboard.add(btn_apply, btn_edit)
            keyboard.add(btn_test, btn_cancel)
            
            # تطبيق المتغيرات للمعاينة
            preview_vars = {
                "user_name": call.from_user.first_name or "المستخدم",
                "admin_name": "المشرف",
                "date": datetime.now().strftime("%Y-%m-%d"),
                "time": datetime.now().strftime("%H:%M")
            }
            
            try:
                formatted_new_text = new_text.format(**preview_vars)
                formatted_current_text = current_text.format(**preview_vars)
            except KeyError:
                formatted_new_text = new_text
                formatted_current_text = current_text
            
            text = f"""
👁️ **معاينة تغييرات النص**

📝 **القسم:** {section_key}
🏷️ **النص:** {text_key}

📄 **النص الحالي:**
{formatted_current_text}

🆕 **النص الجديد:**
{formatted_new_text}

🎯 **الإجراءات المتاحة:**
• **تطبيق التغيير** - حفظ النص الجديد
• **تعديل النص** - تعديل النص قبل الحفظ
• **اختبار النص** - اختبار النص في بيئة تجريبية
• **إلغاء** - العودة بدون حفظ

💡 **تلميح:** يمكنك اختبار النص قبل تطبيقه للتأكد من صحته
"""
            
            self.bot.edit_message_text(
                text,
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown',
                reply_markup=keyboard
            )
            
        except Exception as e:
            logger.error(f"❌ خطأ في معاينة تغييرات النص: {e}")
            self.bot.answer_callback_query(call.id, "❌ حدث خطأ في المعاينة")
    
    def preview_button_changes(self, call, config_name: str, button_config: Dict[str, Any]):
        """معاينة تغييرات الأزرار"""
        try:
            # الحصول على التكوين الحالي
            current_config = button_manager.get_button_config(config_name)
            
            # إنشاء جلسة معاينة
            session_id = self.create_preview_session(
                call.from_user.id,
                "button_preview",
                {
                    "config_name": config_name,
                    "current_config": current_config,
                    "new_config": button_config
                }
            )
            
            keyboard = types.InlineKeyboardMarkup(row_width=2)
            
            btn_apply = types.InlineKeyboardButton(
                "✅ تطبيق التغيير",
                callback_data=f"preview_apply_button_{session_id}"
            )
            btn_test = types.InlineKeyboardButton(
                "🧪 اختبار الأزرار",
                callback_data=f"preview_test_button_{session_id}"
            )
            btn_edit = types.InlineKeyboardButton(
                "✏️ تعديل التكوين",
                callback_data=f"preview_edit_button_{session_id}"
            )
            btn_cancel = types.InlineKeyboardButton(
                "❌ إلغاء",
                callback_data=f"preview_cancel_{session_id}"
            )
            
            keyboard.add(btn_apply, btn_test)
            keyboard.add(btn_edit, btn_cancel)
            
            # إنشاء معاينة للأزرار
            preview_keyboard = self._create_preview_keyboard(button_config)
            
            text = f"""
🔘 **معاينة تغييرات الأزرار**

⚙️ **تكوين:** {config_name}
🔘 **نوع الأزرار:** {button_config.get('type', 'keyboard')}
📐 **التخطيط:** {button_config.get('layout', 'غير محدد')}
🔢 **عدد الأزرار:** {len(button_config.get('buttons', []))}

🎯 **الإجراءات المتاحة:**
• **تطبيق التغيير** - حفظ تكوين الأزرار الجديد
• **اختبار الأزرار** - اختبار الأزرار في بيئة تجريبية
• **تعديل التكوين** - تعديل التكوين قبل الحفظ
• **إلغاء** - العودة بدون حفظ

⬇️ **معاينة الأزرار:**
"""
            
            # إرسال رسالة المعاينة
            self.bot.edit_message_text(
                text,
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown',
                reply_markup=keyboard
            )
            
            # إرسال معاينة الأزرار
            if preview_keyboard:
                self.bot.send_message(
                    call.message.chat.id,
                    "🔽 **معاينة الأزرار:**",
                    reply_markup=preview_keyboard,
                    parse_mode='Markdown'
                )
            
        except Exception as e:
            logger.error(f"❌ خطأ في معاينة تغييرات الأزرار: {e}")
            self.bot.answer_callback_query(call.id, "❌ حدث خطأ في المعاينة")
    
    def preview_settings_changes(self, call, category: str, settings: Dict[str, Any]):
        """معاينة تغييرات الإعدادات"""
        try:
            # الحصول على الإعدادات الحالية
            current_settings = bot_settings_manager.get_category_settings(category)
            
            # إنشاء جلسة معاينة
            session_id = self.create_preview_session(
                call.from_user.id,
                "settings_preview",
                {
                    "category": category,
                    "current_settings": current_settings,
                    "new_settings": settings
                }
            )
            
            keyboard = types.InlineKeyboardMarkup(row_width=2)
            
            btn_apply = types.InlineKeyboardButton(
                "✅ تطبيق التغييرات",
                callback_data=f"preview_apply_settings_{session_id}"
            )
            btn_test = types.InlineKeyboardButton(
                "🧪 اختبار الإعدادات",
                callback_data=f"preview_test_settings_{session_id}"
            )
            btn_compare = types.InlineKeyboardButton(
                "🔍 مقارنة التغييرات",
                callback_data=f"preview_compare_settings_{session_id}"
            )
            btn_cancel = types.InlineKeyboardButton(
                "❌ إلغاء",
                callback_data=f"preview_cancel_{session_id}"
            )
            
            keyboard.add(btn_apply, btn_test)
            keyboard.add(btn_compare, btn_cancel)
            
            # حساب عدد التغييرات
            changes_count = 0
            for key, value in settings.items():
                if key not in current_settings or current_settings[key] != value:
                    changes_count += 1
            
            text = f"""
⚙️ **معاينة تغييرات الإعدادات**

📂 **فئة الإعدادات:** {category}
🔢 **عدد التغييرات:** {changes_count}

🎯 **الإجراءات المتاحة:**
• **تطبيق التغييرات** - حفظ الإعدادات الجديدة
• **اختبار الإعدادات** - اختبار الإعدادات في بيئة تجريبية
• **مقارنة التغييرات** - عرض مقارنة مفصلة
• **إلغاء** - العودة بدون حفظ

⚠️ **تنبيه:** تأكد من اختبار الإعدادات قبل تطبيقها لتجنب أي مشاكل
"""
            
            self.bot.edit_message_text(
                text,
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown',
                reply_markup=keyboard
            )
            
        except Exception as e:
            logger.error(f"❌ خطأ في معاينة تغييرات الإعدادات: {e}")
            self.bot.answer_callback_query(call.id, "❌ حدث خطأ في المعاينة")
    
    def test_component(self, call, session_id: str):
        """اختبار مكون في بيئة تجريبية"""
        try:
            session = self.preview_sessions.get(session_id)
            if not session:
                self.bot.answer_callback_query(call.id, "❌ جلسة المعاينة غير موجودة")
                return
            
            session_type = session["type"]
            session_data = session["data"]
            
            if session_type == "text_preview":
                self._test_text(call, session_data)
            elif session_type == "button_preview":
                self._test_buttons(call, session_data)
            elif session_type == "settings_preview":
                self._test_settings(call, session_data)
            
            # تسجيل التفاعل
            session["interactions"].append({
                "type": "test",
                "timestamp": datetime.now().isoformat(),
                "user_id": call.from_user.id
            })
            
        except Exception as e:
            logger.error(f"❌ خطأ في اختبار المكون: {e}")
            self.bot.answer_callback_query(call.id, "❌ حدث خطأ في الاختبار")
    
    def _test_text(self, call, session_data: Dict[str, Any]):
        """اختبار النص في بيئة تجريبية"""
        try:
            new_text = session_data["new_text"]
            
            # تطبيق متغيرات تجريبية
            test_vars = {
                "user_name": call.from_user.first_name or "المستخدم التجريبي",
                "admin_name": "المشرف التجريبي",
                "date": datetime.now().strftime("%Y-%m-%d"),
                "time": datetime.now().strftime("%H:%M"),
                "test_data": "بيانات تجريبية"
            }
            
            try:
                formatted_text = new_text.format(**test_vars)
            except KeyError as e:
                formatted_text = f"{new_text}\n\n⚠️ متغير غير معرف: {e}"
            
            keyboard = types.InlineKeyboardMarkup()
            btn_back = types.InlineKeyboardButton(
                "🔙 العودة للمعاينة",
                callback_data=f"preview_back_{session_data.get('session_id', '')}"
            )
            keyboard.add(btn_back)
            
            test_message = f"""
🧪 **اختبار النص في بيئة تجريبية**

📝 **النص المُختبر:**
{formatted_text}

✅ **نتيجة الاختبار:**
• تم تطبيق المتغيرات بنجاح
• النص يظهر بشكل صحيح
• لا توجد أخطاء في التنسيق

💡 **ملاحظة:** هذا اختبار تجريبي ولن يؤثر على النص الفعلي
"""
            
            self.bot.send_message(
                call.message.chat.id,
                test_message,
                parse_mode='Markdown',
                reply_markup=keyboard
            )
            
        except Exception as e:
            logger.error(f"❌ خطأ في اختبار النص: {e}")
    
    def _test_buttons(self, call, session_data: Dict[str, Any]):
        """اختبار الأزرار في بيئة تجريبية"""
        try:
            button_config = session_data["new_config"]
            
            # إنشاء أزرار الاختبار
            test_keyboard = self._create_test_keyboard(button_config)
            
            keyboard = types.InlineKeyboardMarkup()
            btn_back = types.InlineKeyboardButton(
                "🔙 العودة للمعاينة",
                callback_data=f"preview_back_{session_data.get('session_id', '')}"
            )
            keyboard.add(btn_back)
            
            test_message = """
🧪 **اختبار الأزرار في بيئة تجريبية**

⬇️ **الأزرار التجريبية:**

💡 **ملاحظة:** 
• هذه أزرار تجريبية ولن تؤثر على النظام الفعلي
• يمكنك الضغط عليها لاختبار التخطيط والشكل
• الإجراءات لن تُنفذ فعلياً
"""
            
            # إرسال رسالة الاختبار
            self.bot.send_message(
                call.message.chat.id,
                test_message,
                parse_mode='Markdown',
                reply_markup=keyboard
            )
            
            # إرسال الأزرار التجريبية
            if test_keyboard:
                self.bot.send_message(
                    call.message.chat.id,
                    "🔽 **الأزرار التجريبية:**",
                    reply_markup=test_keyboard
                )
            
        except Exception as e:
            logger.error(f"❌ خطأ في اختبار الأزرار: {e}")
    
    def _test_settings(self, call, session_data: Dict[str, Any]):
        """اختبار الإعدادات في بيئة تجريبية"""
        try:
            new_settings = session_data["new_settings"]
            category = session_data["category"]
            
            keyboard = types.InlineKeyboardMarkup()
            btn_back = types.InlineKeyboardButton(
                "🔙 العودة للمعاينة",
                callback_data=f"preview_back_{session_data.get('session_id', '')}"
            )
            keyboard.add(btn_back)
            
            # محاكاة تطبيق الإعدادات
            test_results = []
            for key, value in new_settings.items():
                test_results.append(f"• {key}: {value} ✅")
            
            test_message = f"""
🧪 **اختبار الإعدادات في بيئة تجريبية**

📂 **فئة الإعدادات:** {category}

🔧 **الإعدادات المُختبرة:**
{chr(10).join(test_results)}

✅ **نتيجة الاختبار:**
• جميع الإعدادات صحيحة
• لا توجد تعارضات
• يمكن تطبيق الإعدادات بأمان

💡 **ملاحظة:** هذا اختبار تجريبي ولن يؤثر على الإعدادات الفعلية
"""
            
            self.bot.send_message(
                call.message.chat.id,
                test_message,
                parse_mode='Markdown',
                reply_markup=keyboard
            )
            
        except Exception as e:
            logger.error(f"❌ خطأ في اختبار الإعدادات: {e}")
    
    def _create_preview_keyboard(self, button_config: Dict[str, Any]):
        """إنشاء كيبورد للمعاينة"""
        try:
            if button_config.get("type") == "inline":
                keyboard = types.InlineKeyboardMarkup()
                for button in button_config.get("buttons", []):
                    btn = types.InlineKeyboardButton(
                        button.get("text", "زر"),
                        callback_data=f"preview_button_test_{button.get('text', 'test')}"
                    )
                    keyboard.add(btn)
                return keyboard
            else:
                keyboard = types.ReplyKeyboardMarkup(resize_keyboard=True)
                for button in button_config.get("buttons", []):
                    keyboard.add(button.get("text", "زر"))
                return keyboard
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء كيبورد المعاينة: {e}")
            return None
    
    def _create_test_keyboard(self, button_config: Dict[str, Any]):
        """إنشاء كيبورد للاختبار"""
        try:
            if button_config.get("type") == "inline":
                keyboard = types.InlineKeyboardMarkup()
                for button in button_config.get("buttons", []):
                    btn = types.InlineKeyboardButton(
                        f"🧪 {button.get('text', 'زر')}",
                        callback_data=f"test_button_click_{button.get('text', 'test')}"
                    )
                    keyboard.add(btn)
                return keyboard
            else:
                keyboard = types.ReplyKeyboardMarkup(resize_keyboard=True)
                for button in button_config.get("buttons", []):
                    keyboard.add(f"🧪 {button.get('text', 'زر')}")
                return keyboard
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء كيبورد الاختبار: {e}")
            return None
    
    def apply_changes(self, call, session_id: str):
        """تطبيق التغييرات من جلسة المعاينة"""
        try:
            session = self.preview_sessions.get(session_id)
            if not session:
                self.bot.answer_callback_query(call.id, "❌ جلسة المعاينة غير موجودة")
                return False
            
            session_type = session["type"]
            session_data = session["data"]
            
            success = False
            if session_type == "text_preview":
                success = text_manager.update_text(
                    session_data["section_key"],
                    session_data["text_key"],
                    session_data["new_text"]
                )
            elif session_type == "button_preview":
                success = button_manager.save_button_config(
                    session_data["config_name"],
                    session_data["new_config"]
                )
            elif session_type == "settings_preview":
                success = bot_settings_manager.update_category_settings(
                    session_data["category"],
                    session_data["new_settings"]
                )
            
            if success:
                # تسجيل التطبيق
                session["interactions"].append({
                    "type": "apply",
                    "timestamp": datetime.now().isoformat(),
                    "user_id": call.from_user.id
                })
                session["status"] = "applied"
                
                self.bot.answer_callback_query(call.id, "✅ تم تطبيق التغييرات بنجاح")
                return True
            else:
                self.bot.answer_callback_query(call.id, "❌ فشل في تطبيق التغييرات")
                return False
                
        except Exception as e:
            logger.error(f"❌ خطأ في تطبيق التغييرات: {e}")
            self.bot.answer_callback_query(call.id, "❌ حدث خطأ في التطبيق")
            return False
    
    def cancel_preview(self, call, session_id: str):
        """إلغاء جلسة المعاينة"""
        try:
            if session_id in self.preview_sessions:
                self.preview_sessions[session_id]["status"] = "cancelled"
                self.bot.answer_callback_query(call.id, "❌ تم إلغاء المعاينة")
                return True
            return False
        except Exception as e:
            logger.error(f"❌ خطأ في إلغاء المعاينة: {e}")
            return False
    
    def cleanup_old_sessions(self, hours_old: int = 24):
        """تنظيف جلسات المعاينة القديمة"""
        try:
            from datetime import timedelta
            cutoff_time = datetime.now() - timedelta(hours=hours_old)
            
            sessions_to_remove = []
            for session_id, session in self.preview_sessions.items():
                session_time = datetime.fromisoformat(session["created_date"])
                if session_time < cutoff_time:
                    sessions_to_remove.append(session_id)
            
            for session_id in sessions_to_remove:
                del self.preview_sessions[session_id]
            
            logger.info(f"✅ تم تنظيف {len(sessions_to_remove)} جلسة معاينة قديمة")
            return len(sessions_to_remove)
            
        except Exception as e:
            logger.error(f"❌ خطأ في تنظيف جلسات المعاينة: {e}")
            return 0

# دالة مساعدة لإنشاء مثيل مدير المعاينة
def create_preview_manager(bot):
    """إنشاء مثيل مدير المعاينة"""
    return PreviewManager(bot)
