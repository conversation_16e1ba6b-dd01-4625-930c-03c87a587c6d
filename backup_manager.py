#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير النسخ الاحتياطي والاستيراد/التصدير للبوت التعليمي
Backup Manager and Import/Export System for Educational Bot

يدير إنشاء واستعادة النسخ الاحتياطية وعمليات الاستيراد والتصدير
"""

import logging
import json
import zipfile
import os
from typing import Dict, Any, Optional, List
from datetime import datetime
from firebase_manager import firebase_manager
from text_manager import text_manager
from button_manager import button_manager
from bot_settings_manager import bot_settings_manager
from file_manager import file_manager

logger = logging.getLogger(__name__)

class BackupManager:
    """مدير النسخ الاحتياطي والاستيراد/التصدير"""
    
    def __init__(self):
        self.backup_settings = {}
        self.load_backup_settings()
    
    def load_backup_settings(self):
        """تحميل إعدادات النسخ الاحتياطي"""
        try:
            self.backup_settings = firebase_manager.get_backup_settings() or self.get_default_backup_settings()
            logger.info("✅ تم تحميل إعدادات النسخ الاحتياطي")
        except Exception as e:
            logger.error(f"❌ خطأ في تحميل إعدادات النسخ الاحتياطي: {e}")
            self.backup_settings = self.get_default_backup_settings()
    
    def get_default_backup_settings(self) -> Dict[str, Any]:
        """الإعدادات الافتراضية للنسخ الاحتياطي"""
        return {
            "auto_backup": {
                "enabled": True,
                "interval_hours": 24,
                "max_backups": 7,
                "include_files": False,
                "compress": True,
                "encrypt": False
            },
            "manual_backup": {
                "include_texts": True,
                "include_buttons": True,
                "include_settings": True,
                "include_content": True,
                "include_mcq": True,
                "include_services": True,
                "include_users": False,
                "include_files": False,
                "compress": True,
                "encrypt": False
            },
            "export_formats": {
                "json": True,
                "csv": True,
                "xml": False,
                "yaml": False
            },
            "storage": {
                "local_path": "./backups/",
                "firebase_storage": True,
                "cloud_storage": False
            }
        }
    
    def create_full_backup(self, include_files: bool = False, compress: bool = True) -> Dict[str, Any]:
        """إنشاء نسخة احتياطية كاملة"""
        try:
            backup_id = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            backup_data = {
                "backup_info": {
                    "id": backup_id,
                    "created_date": datetime.now().isoformat(),
                    "type": "full_backup",
                    "version": "2.0",
                    "include_files": include_files,
                    "compressed": compress
                },
                "bot_texts": text_manager.get_all_texts(),
                "bot_settings": bot_settings_manager.get_all_settings(),
                "dynamic_buttons": button_manager.get_all_button_configs(),
                "mcq_questions": firebase_manager.get_mcq_questions() or {},
                "educational_content": firebase_manager.get_educational_content() or {},
                "services_data": firebase_manager.get_services_data() or {},
                "specialists_data": firebase_manager.get_specialists_data() or {},
                "admin_ids": firebase_manager.get_admin_ids() or []
            }
            
            # إضافة بيانات الملفات إذا طُلب ذلك
            if include_files:
                backup_data["files_data"] = file_manager.export_file_list()
            
            # حفظ النسخة الاحتياطية
            backup_path = self._save_backup(backup_id, backup_data, compress)
            
            if backup_path:
                logger.info(f"✅ تم إنشاء النسخة الاحتياطية: {backup_id}")
                return {
                    "success": True,
                    "backup_id": backup_id,
                    "backup_path": backup_path,
                    "size": self._get_file_size(backup_path)
                }
            else:
                return {"success": False, "error": "فشل في حفظ النسخة الاحتياطية"}
                
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء النسخة الاحتياطية: {e}")
            return {"success": False, "error": str(e)}
    
    def create_selective_backup(self, components: List[str], compress: bool = True) -> Dict[str, Any]:
        """إنشاء نسخة احتياطية انتقائية"""
        try:
            backup_id = f"selective_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            backup_data = {
                "backup_info": {
                    "id": backup_id,
                    "created_date": datetime.now().isoformat(),
                    "type": "selective_backup",
                    "version": "2.0",
                    "components": components,
                    "compressed": compress
                }
            }
            
            # إضافة المكونات المحددة
            if "texts" in components:
                backup_data["bot_texts"] = text_manager.get_all_texts()
            
            if "settings" in components:
                backup_data["bot_settings"] = bot_settings_manager.get_all_settings()
            
            if "buttons" in components:
                backup_data["dynamic_buttons"] = button_manager.get_all_button_configs()
            
            if "mcq" in components:
                backup_data["mcq_questions"] = firebase_manager.get_mcq_questions() or {}
            
            if "content" in components:
                backup_data["educational_content"] = firebase_manager.get_educational_content() or {}
            
            if "services" in components:
                backup_data["services_data"] = firebase_manager.get_services_data() or {}
                backup_data["specialists_data"] = firebase_manager.get_specialists_data() or {}
            
            if "admins" in components:
                backup_data["admin_ids"] = firebase_manager.get_admin_ids() or []
            
            if "files" in components:
                backup_data["files_data"] = file_manager.export_file_list()
            
            # حفظ النسخة الاحتياطية
            backup_path = self._save_backup(backup_id, backup_data, compress)
            
            if backup_path:
                logger.info(f"✅ تم إنشاء النسخة الاحتياطية الانتقائية: {backup_id}")
                return {
                    "success": True,
                    "backup_id": backup_id,
                    "backup_path": backup_path,
                    "components": components,
                    "size": self._get_file_size(backup_path)
                }
            else:
                return {"success": False, "error": "فشل في حفظ النسخة الاحتياطية"}
                
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء النسخة الاحتياطية الانتقائية: {e}")
            return {"success": False, "error": str(e)}
    
    def restore_backup(self, backup_path: str, components: List[str] = None) -> Dict[str, Any]:
        """استعادة نسخة احتياطية"""
        try:
            # تحميل النسخة الاحتياطية
            backup_data = self._load_backup(backup_path)
            if not backup_data:
                return {"success": False, "error": "فشل في تحميل النسخة الاحتياطية"}
            
            restored_components = []
            
            # استعادة المكونات المحددة أو جميع المكونات
            if not components:
                components = list(backup_data.keys())
            
            for component in components:
                if component == "backup_info":
                    continue
                    
                if component == "bot_texts" and component in backup_data:
                    if firebase_manager.save_bot_texts(backup_data[component]):
                        text_manager.reload_texts()
                        restored_components.append("النصوص")
                
                elif component == "bot_settings" and component in backup_data:
                    if firebase_manager.save_bot_settings(backup_data[component]):
                        bot_settings_manager.reload_settings()
                        restored_components.append("الإعدادات")
                
                elif component == "dynamic_buttons" and component in backup_data:
                    if firebase_manager.save_dynamic_buttons(backup_data[component]):
                        button_manager.reload_buttons()
                        restored_components.append("الأزرار")
                
                elif component == "mcq_questions" and component in backup_data:
                    if firebase_manager.save_mcq_questions(backup_data[component]):
                        restored_components.append("أسئلة MCQ")
                
                elif component == "educational_content" and component in backup_data:
                    if firebase_manager.save_educational_content(backup_data[component]):
                        restored_components.append("المحتوى التعليمي")
                
                elif component == "services_data" and component in backup_data:
                    if firebase_manager.save_services_data(backup_data[component]):
                        restored_components.append("بيانات الخدمات")
                
                elif component == "specialists_data" and component in backup_data:
                    if firebase_manager.save_specialists_data(backup_data[component]):
                        restored_components.append("بيانات المختصين")
                
                elif component == "admin_ids" and component in backup_data:
                    if firebase_manager.save_admin_ids(backup_data[component]):
                        restored_components.append("معرفات المشرفين")
            
            if restored_components:
                logger.info(f"✅ تم استعادة المكونات: {', '.join(restored_components)}")
                return {
                    "success": True,
                    "restored_components": restored_components,
                    "backup_info": backup_data.get("backup_info", {})
                }
            else:
                return {"success": False, "error": "لم يتم استعادة أي مكونات"}
                
        except Exception as e:
            logger.error(f"❌ خطأ في استعادة النسخة الاحتياطية: {e}")
            return {"success": False, "error": str(e)}
    
    def export_data(self, data_type: str, format_type: str = "json") -> Dict[str, Any]:
        """تصدير البيانات بصيغ مختلفة"""
        try:
            export_data = {}
            filename = f"export_{data_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # تحديد البيانات المراد تصديرها
            if data_type == "texts":
                export_data = text_manager.get_all_texts()
                filename += "_texts"
            elif data_type == "settings":
                export_data = bot_settings_manager.get_all_settings()
                filename += "_settings"
            elif data_type == "buttons":
                export_data = button_manager.get_all_button_configs()
                filename += "_buttons"
            elif data_type == "mcq":
                export_data = firebase_manager.get_mcq_questions() or {}
                filename += "_mcq"
            elif data_type == "content":
                export_data = firebase_manager.get_educational_content() or {}
                filename += "_content"
            elif data_type == "services":
                export_data = {
                    "services": firebase_manager.get_services_data() or {},
                    "specialists": firebase_manager.get_specialists_data() or {}
                }
                filename += "_services"
            elif data_type == "files":
                export_data = file_manager.export_file_list()
                filename += "_files"
            else:
                return {"success": False, "error": "نوع البيانات غير مدعوم"}
            
            # تحديد مسار الحفظ
            export_path = os.path.join(self.backup_settings["storage"]["local_path"], "exports")
            os.makedirs(export_path, exist_ok=True)
            
            # حفظ البيانات بالصيغة المحددة
            if format_type == "json":
                file_path = os.path.join(export_path, f"{filename}.json")
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, ensure_ascii=False, indent=2)
            
            elif format_type == "csv":
                file_path = os.path.join(export_path, f"{filename}.csv")
                self._export_to_csv(export_data, file_path)
            
            else:
                return {"success": False, "error": "صيغة التصدير غير مدعومة"}
            
            logger.info(f"✅ تم تصدير البيانات: {filename}")
            return {
                "success": True,
                "file_path": file_path,
                "data_type": data_type,
                "format": format_type,
                "size": self._get_file_size(file_path)
            }
            
        except Exception as e:
            logger.error(f"❌ خطأ في تصدير البيانات: {e}")
            return {"success": False, "error": str(e)}
    
    def import_data(self, file_path: str, data_type: str, merge: bool = False) -> Dict[str, Any]:
        """استيراد البيانات من ملف"""
        try:
            # تحميل البيانات من الملف
            if file_path.endswith('.json'):
                with open(file_path, 'r', encoding='utf-8') as f:
                    import_data = json.load(f)
            else:
                return {"success": False, "error": "صيغة الملف غير مدعومة"}
            
            # استيراد البيانات حسب النوع
            success = False
            if data_type == "texts":
                if merge:
                    current_texts = text_manager.get_all_texts()
                    current_texts.update(import_data)
                    success = firebase_manager.save_bot_texts(current_texts)
                else:
                    success = firebase_manager.save_bot_texts(import_data)
                if success:
                    text_manager.reload_texts()
            
            elif data_type == "settings":
                if merge:
                    current_settings = bot_settings_manager.get_all_settings()
                    current_settings.update(import_data)
                    success = firebase_manager.save_bot_settings(current_settings)
                else:
                    success = firebase_manager.save_bot_settings(import_data)
                if success:
                    bot_settings_manager.reload_settings()
            
            elif data_type == "buttons":
                if merge:
                    current_buttons = button_manager.get_all_button_configs()
                    current_buttons.update(import_data)
                    success = firebase_manager.save_dynamic_buttons(current_buttons)
                else:
                    success = firebase_manager.save_dynamic_buttons(import_data)
                if success:
                    button_manager.reload_buttons()
            
            else:
                return {"success": False, "error": "نوع البيانات غير مدعوم للاستيراد"}
            
            if success:
                logger.info(f"✅ تم استيراد البيانات: {data_type}")
                return {
                    "success": True,
                    "data_type": data_type,
                    "merge": merge,
                    "file_path": file_path
                }
            else:
                return {"success": False, "error": "فشل في حفظ البيانات المستوردة"}
                
        except Exception as e:
            logger.error(f"❌ خطأ في استيراد البيانات: {e}")
            return {"success": False, "error": str(e)}
    
    def _save_backup(self, backup_id: str, backup_data: Dict[str, Any], compress: bool = True) -> Optional[str]:
        """حفظ النسخة الاحتياطية"""
        try:
            # إنشاء مجلد النسخ الاحتياطية
            backup_path = self.backup_settings["storage"]["local_path"]
            os.makedirs(backup_path, exist_ok=True)
            
            if compress:
                # حفظ كملف مضغوط
                zip_path = os.path.join(backup_path, f"{backup_id}.zip")
                with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                    json_data = json.dumps(backup_data, ensure_ascii=False, indent=2)
                    zipf.writestr(f"{backup_id}.json", json_data)
                return zip_path
            else:
                # حفظ كملف JSON
                json_path = os.path.join(backup_path, f"{backup_id}.json")
                with open(json_path, 'w', encoding='utf-8') as f:
                    json.dump(backup_data, f, ensure_ascii=False, indent=2)
                return json_path
                
        except Exception as e:
            logger.error(f"❌ خطأ في حفظ النسخة الاحتياطية: {e}")
            return None
    
    def _load_backup(self, backup_path: str) -> Optional[Dict[str, Any]]:
        """تحميل النسخة الاحتياطية"""
        try:
            if backup_path.endswith('.zip'):
                # تحميل من ملف مضغوط
                with zipfile.ZipFile(backup_path, 'r') as zipf:
                    json_files = [f for f in zipf.namelist() if f.endswith('.json')]
                    if json_files:
                        with zipf.open(json_files[0]) as f:
                            return json.load(f)
            else:
                # تحميل من ملف JSON
                with open(backup_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return None
        except Exception as e:
            logger.error(f"❌ خطأ في تحميل النسخة الاحتياطية: {e}")
            return None
    
    def _export_to_csv(self, data: Dict[str, Any], file_path: str):
        """تصدير البيانات إلى CSV"""
        try:
            import csv
            with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                # هذه دالة مبسطة - يمكن تطويرها حسب نوع البيانات
                writer = csv.writer(csvfile)
                writer.writerow(['Key', 'Value'])
                for key, value in data.items():
                    writer.writerow([key, str(value)])
        except Exception as e:
            logger.error(f"❌ خطأ في تصدير CSV: {e}")
    
    def _get_file_size(self, file_path: str) -> str:
        """الحصول على حجم الملف"""
        try:
            size_bytes = os.path.getsize(file_path)
            if size_bytes < 1024:
                return f"{size_bytes} بايت"
            elif size_bytes < 1024 * 1024:
                return f"{size_bytes / 1024:.1f} كيلوبايت"
            else:
                return f"{size_bytes / (1024 * 1024):.1f} ميجابايت"
        except Exception:
            return "غير معروف"
    
    def get_backup_list(self) -> List[Dict[str, Any]]:
        """الحصول على قائمة النسخ الاحتياطية"""
        try:
            backup_path = self.backup_settings["storage"]["local_path"]
            if not os.path.exists(backup_path):
                return []
            
            backups = []
            for filename in os.listdir(backup_path):
                if filename.endswith(('.json', '.zip')):
                    file_path = os.path.join(backup_path, filename)
                    stat = os.stat(file_path)
                    backups.append({
                        "filename": filename,
                        "path": file_path,
                        "size": self._get_file_size(file_path),
                        "created_date": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                        "modified_date": datetime.fromtimestamp(stat.st_mtime).isoformat()
                    })
            
            # ترتيب حسب تاريخ الإنشاء (الأحدث أولاً)
            backups.sort(key=lambda x: x["created_date"], reverse=True)
            return backups
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على قائمة النسخ الاحتياطية: {e}")
            return []

# إنشاء مثيل مدير النسخ الاحتياطي
backup_manager = BackupManager()
