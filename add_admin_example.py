#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مثال بسيط لإضافة مشرف جديد
استخدم هذا الملف كمثال لإضافة مشرف بسرعة
"""

import os
import sys
from datetime import datetime
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from firebase_manager import FirebaseManager

def add_admin_example():
    """مثال على إضافة مشرف"""
    try:
        firebase_manager = FirebaseManager()
        
        # بيانات المشرف الجديد (غير هذه البيانات حسب الحاجة)
        telegram_id = 123456789  # ضع هنا Telegram ID الحقيقي
        name = "أحم<PERSON> محمد"        # ضع هنا الاسم المطلوب
        role = "مشرف المحتوى"    # ضع هنا الدور المطلوب
        
        print(f"🔄 جاري إضافة المشرف...")
        print(f"🆔 ID: {telegram_id}")
        print(f"📝 الاسم: {name}")
        print(f"🎭 الدور: {role}")
        
        # التحقق من وجود المشرف مسبقاً
        existing_admins = firebase_manager.get_admin_ids()
        if telegram_id in existing_admins:
            print(f"⚠️ المشرف {telegram_id} موجود مسبقاً")
            return False
        
        # بيانات المشرف
        admin_data = {
            'telegram_id': telegram_id,
            'name': name,
            'role': role,
            'active': True,
            'created_date': datetime.now(),
            'permissions': {
                'manage_users': True,
                'manage_content': True,
                'view_statistics': True,
                'manage_ideas': True
            }
        }
        
        # إضافة المشرف
        success = firebase_manager.add_admin(telegram_id, admin_data)
        
        if success:
            print(f"✅ تم إضافة المشرف {telegram_id} بنجاح")
            print(f"📝 الاسم: {admin_data['name']}")
            print(f"🎭 الدور: {admin_data['role']}")
            print("🔄 ستتم إضافة معلوماته الكاملة عند دخوله للبوت لأول مرة")
            return True
        else:
            print(f"❌ فشل في إضافة المشرف {telegram_id}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def list_admins_example():
    """مثال على عرض قائمة المشرفين"""
    try:
        firebase_manager = FirebaseManager()
        
        admin_ids = firebase_manager.get_admin_ids()
        
        if not admin_ids:
            print("📋 لا يوجد مشرفين مسجلين")
            return
        
        print(f"📋 قائمة المشرفين ({len(admin_ids)} مشرف):")
        print("=" * 50)
        
        for admin_id in admin_ids:
            admin_data = firebase_manager.get_admin_details(admin_id)
            if admin_data:
                name = admin_data.get('name', 'غير محدد')
                role = admin_data.get('role', 'مشرف')
                active = admin_data.get('active', False)
                
                print(f"🆔 ID: {admin_id}")
                print(f"📝 الاسم: {name}")
                print(f"🎭 الدور: {role}")
                print(f"✅ نشط: {'نعم' if active else 'لا'}")
                
                if admin_data.get('username'):
                    print(f"👤 اسم المستخدم: @{admin_data.get('username')}")
                if admin_data.get('created_date'):
                    created_date = admin_data.get('created_date')
                    if hasattr(created_date, 'strftime'):
                        print(f"📅 تاريخ الإضافة: {created_date.strftime('%Y-%m-%d %H:%M')}")
                
                print("-" * 30)
            else:
                print(f"🆔 ID: {admin_id} (بيانات غير مكتملة)")
                print("-" * 30)
                
    except Exception as e:
        print(f"❌ خطأ في عرض المشرفين: {e}")

def main():
    """الدالة الرئيسية"""
    print("🚀 مثال على إدارة المشرفين")
    print("=" * 40)
    
    print("\n1️⃣ عرض المشرفين الحاليين:")
    list_admins_example()
    
    print("\n2️⃣ إضافة مشرف جديد:")
    print("⚠️ تأكد من تغيير البيانات في الكود قبل التشغيل!")
    
    # إلغاء التعليق عن السطر التالي لإضافة المشرف
    # add_admin_example()
    
    print("\n💡 لإضافة مشرف جديد:")
    print("1. افتح الملف add_admin_example.py")
    print("2. غير البيانات في دالة add_admin_example()")
    print("3. ألغ التعليق عن السطر add_admin_example()")
    print("4. شغل الملف مرة أخرى")

if __name__ == "__main__":
    main()
