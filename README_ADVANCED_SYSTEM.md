# 🎓 النظام المتقدم للبوت التعليمي

## 📋 نظرة عامة

تم تطوير نظام إدارة متقدم وشامل للبوت التعليمي يتيح للمشرفين التحكم الكامل في جميع جوانب البوت من خلال واجهة إدارية احترافية.

## 🏗️ هيكل النظام الجديد

### 📁 الملفات الأساسية

1. **`educational_bot_fixed.py`** - الملف الرئيسي للبوت
2. **`firebase_manager.py`** - إدارة قاعدة البيانات
3. **`text_manager.py`** - إدارة النصوص الديناميكية
4. **`button_manager.py`** - إدارة الأزرار الديناميكية
5. **`mcq_manager.py`** - إدارة أسئلة MCQ المتقدمة
6. **`content_manager.py`** - إدارة المحتوى والأقسام
7. **`admin_panel.py`** - لوحة التحكم الإدارية
8. **`text_editor.py`** - محرر النصوص التفاعلي
9. **`question_editor.py`** - محرر الأسئلة التفاعلي
10. **`file_manager.py`** - إدارة الملفات والوسائط

## 🎯 المميزات الجديدة

### 🔤 نظام إدارة النصوص الديناميكي

- **تحرير جميع النصوص** من لوحة التحكم
- **متغيرات ديناميكية** مثل `{user_name}` و `{date}`
- **معاينة فورية** للتغييرات
- **تصدير واستيراد** النصوص
- **إعادة تعيين افتراضية** للنصوص

#### الأقسام المدعومة:
- نصوص الترحيب
- نصوص الترجمة
- نصوص المحتوى العلمي
- نصوص المواد الوزارية
- نصوص الخدمات
- نصوص اقتراح الأفكار
- نصوص الإدارة
- النصوص العامة

### 🔘 نظام إدارة الأزرار الديناميكي

- **إنشاء أزرار مخصصة** (Inline & Keyboard)
- **تخصيص التخطيط** والترتيب
- **ربط الأزرار بالإجراءات** والروابط
- **معاينة الأزرار** قبل النشر
- **قوالب جاهزة** للأزرار الشائعة

#### أنواع الأزرار:
- أزرار القائمة الرئيسية
- أزرار لوحة التحكم
- أزرار العودة والتنقل
- أزرار مخصصة للأقسام

### ❓ نظام أسئلة MCQ المتقدم

- **إضافة أسئلة تفاعلية** مع محرر متقدم
- **خلط الأسئلة والإجابات** عشوائياً
- **تحديد عدد الأسئلة** في كل اختبار
- **إعدادات مخصصة** لكل مادة
- **إحصائيات الأداء** والتحليل

#### مميزات الأسئلة:
- أسئلة اختيار من متعدد (4-5 خيارات)
- أسئلة صح/خطأ
- شرح مفصل للإجابات
- مستويات صعوبة متدرجة
- تصنيف حسب الفئات

### 📚 نظام إدارة المحتوى المتقدم

- **هيكل هرمي** للمحتوى (مراحل → مواد → مواضيع → موارد)
- **إضافة محتوى ديناميكي** من لوحة التحكم
- **تنظيم الملفات** والوسائط
- **ربط المحتوى بالأزرار** التفاعلية

#### أنواع المحتوى:
- المحتوى التعليمي (مراحل دراسية)
- المواد الوزارية (أسئلة وامتحانات)
- الخدمات (فئات ومختصين)

### 🛠️ نظام إدارة الخدمات

- **فئات خدمات متنوعة**
- **إدارة المختصين** وبياناتهم
- **أمثلة الأعمال** (Portfolio)
- **طلبات الخدمات** ومتابعتها
- **تقييمات ومراجعات**

### 📁 نظام إدارة الملفات

- **رفع وتنظيم الملفات** بفئات مختلفة
- **التحقق من الأمان** وفحص الفيروسات
- **ضغط تلقائي** وإنشاء صور مصغرة
- **إحصائيات الاستخدام** والتخزين
- **تنظيف تلقائي** للملفات القديمة

## 🎛️ لوحة التحكم الإدارية

### 📊 الأقسام الرئيسية

1. **📊 الإحصائيات** - عرض شامل لإحصائيات البوت
2. **👥 إدارة المستخدمين** - عرض وإدارة المستخدمين
3. **💡 الأفكار المقترحة** - مراجعة الأفكار الجديدة
4. **📚 إدارة المحتوى** - تحديث المحتوى التعليمي
5. **🔤 إدارة النصوص** - تخصيص نصوص البوت
6. **🔘 إدارة الأزرار** - تخصيص أزرار البوت
7. **❓ إدارة الأسئلة** - إدارة أسئلة MCQ
8. **👨‍💼 إدارة المشرفين** - إضافة/إزالة المشرفين
9. **📢 رسالة جماعية** - إرسال رسائل لجميع المستخدمين
10. **💾 النسخ الاحتياطي** - إنشاء واستعادة النسخ
11. **⚙️ إعدادات البوت** - تخصيص إعدادات البوت
12. **📋 سجلات النشاط** - عرض سجلات النشاط

### 🔐 نظام الصلاحيات

- **تحديد صلاحيات مختلفة** لكل مشرف
- **مستويات وصول متدرجة**
- **تتبع نشاط المشرفين**
- **إدارة آمنة للحسابات**

## ⚙️ الإعدادات المتقدمة

### 🎭 سلوك البوت

- **طريقة عرض الرسائل** (تعديل/جديدة)
- **تأخير الكتابة** والاستجابة
- **الحذف التلقائي** للرسائل
- **إعدادات التفاعل** مع المستخدمين

### 🔔 الإشعارات

- **إشعارات callback query**
- **إشعارات النجاح والأخطاء**
- **إشعارات المشرفين**
- **تخصيص نصوص الإشعارات**

### 🎯 المميزات

- **تفعيل/إلغاء الترجمة**
- **تفعيل/إلغاء أسئلة MCQ**
- **تفعيل/إلغاء رفع الملفات**
- **تفعيل/إلغاء اقتراح الأفكار**

### ⚖️ الحدود والقيود

- **حد حجم الملفات** المرفوعة
- **حد طول النصوص**
- **عدد الأسئلة** في الاختبار
- **وقت الاختبار** المحدد

### 🎨 المظهر

- **استخدام الرموز التعبيرية**
- **تفعيل Markdown**
- **نمط الأزرار**
- **نظام الألوان**

## 🚀 كيفية الاستخدام

### 1. بدء التشغيل

```bash
python educational_bot_fixed.py
```

### 2. إضافة مشرف جديد

```
/add_admin - إضافة المستخدم الحالي كمشرف
/my_id - عرض معرف المستخدم
```

### 3. الوصول للوحة التحكم

- اضغط على زر "🔧 لوحة التحكم" في القائمة الرئيسية
- أو أرسل الأمر `/admin`

### 4. تحرير النصوص

1. اذهب إلى "🔤 إدارة النصوص"
2. اختر القسم المطلوب
3. اختر النص المراد تحريره
4. اكتب النص الجديد
5. احفظ التغييرات

### 5. إضافة أسئلة MCQ

1. اذهب إلى "❓ إدارة الأسئلة"
2. اختر المادة أو أضف مادة جديدة
3. اضغط "➕ إضافة سؤال جديد"
4. اختر نوع السؤال
5. اتبع الخطوات التفاعلية

### 6. إدارة المحتوى

1. اذهب إلى "📚 إدارة المحتوى"
2. اختر نوع المحتوى (تعليمي/وزاري/خدمات)
3. أضف أو عدل المحتوى المطلوب
4. احفظ التغييرات

## 🔧 التخصيص المتقدم

### إضافة متغيرات جديدة

```python
# في text_manager.py
def get_text(self, section: str, key: str, **kwargs) -> str:
    text = self.texts[section][key]
    if kwargs:
        text = text.format(**kwargs)
    return text
```

### إضافة أنواع أزرار جديدة

```python
# في button_manager.py
def add_button_config(self, config_name: str, config: Dict[str, Any]) -> bool:
    self.buttons_config[config_name] = config
    return firebase_manager.save_dynamic_buttons(self.buttons_config)
```

### إضافة أنواع أسئلة جديدة

```python
# في question_editor.py
def get_question_templates(self) -> Dict[str, Any]:
    return {
        "new_type": {
            "name": "نوع جديد",
            "template": {...}
        }
    }
```

## 📊 قاعدة البيانات

### Firebase Collections

- **`admins`** - بيانات المشرفين
- **`users`** - بيانات المستخدمين
- **`bot_texts`** - نصوص البوت
- **`bot_settings`** - إعدادات البوت
- **`dynamic_buttons`** - تكوين الأزرار
- **`mcq_questions`** - أسئلة MCQ
- **`educational_content`** - المحتوى التعليمي
- **`ministerial_questions`** - الأسئلة الوزارية
- **`services`** - بيانات الخدمات
- **`suggested_ideas`** - الأفكار المقترحة
- **`test_results`** - نتائج الاختبارات
- **`user_activities`** - أنشطة المستخدمين

## 🛡️ الأمان

- **تشفير البيانات الحساسة**
- **التحقق من صلاحيات الوصول**
- **تسجيل جميع العمليات**
- **نسخ احتياطية تلقائية**
- **فحص الملفات المرفوعة**

## 🔄 التحديثات المستقبلية

- [ ] نظام التقييمات والمراجعات
- [ ] إشعارات push للمستخدمين
- [ ] تحليلات متقدمة للأداء
- [ ] دعم اللغات المتعددة
- [ ] تطبيق ويب للإدارة
- [ ] API للتكامل الخارجي

## 📞 الدعم الفني

للحصول على الدعم الفني أو الإبلاغ عن مشاكل:

- **المطور:** كرار الحدراوي
- **التاريخ:** 2025-07-12
- **الإصدار:** 2.0 Advanced

---

**ملاحظة:** هذا النظام قابل للتوسع والتخصيص بالكامل حسب احتياجاتك المحددة.
